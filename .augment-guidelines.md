# Augment User Guidelines

## 1. ✅ Code Quality & Readability

- Write clean, modular code with clear intent
- Use descriptive naming that aligns with domain context
- Keep functions under 30 lines - break complex logic into smaller units
- Implement early returns and guard clauses to reduce nesting
- Add comments for non-obvious logic, focusing on "why" not "what"
- Optimize CSS with containment and will-change properties for performance

## 2. 🔄 Reusability & DRY Principle

- Follow DRY (Don't Repeat Yourself) principle rigorously
- Extract reusable logic into utility functions in `/app/utils/`
- Use the RORO pattern (Receive Object, Return Object) for complex functions
- Implement modular JS structure as shown in `app/static/js/structure.md`

## 3. 📁 Project Structure & Organization

- Group by feature/module following Flask blueprint architecture
- Maintain consistent folder structure across projects
- Separate concerns: routes, business logic, data access, UI components
- Use naming conventions that reflect purpose (e.g., `ajax_helpers.py`, `asset-manager.js`)

## 4. 📐 Design Patterns & Architecture

- Follow Flask best practices with blueprints for modularity
- Prefer functional, declarative programming over classes (except for Flask views)
- Implement clear separation of concerns in all components
- Use environment variables for configuration management

## 5. 🛡️ Security & Performance

- Validate all user inputs at every layer
- Implement proper database session management
- Optimize performance with techniques from `theme-transition.css`:
  - Use CSS containment and will-change
  - Implement hardware acceleration for animations
  - Apply targeted transitions to high-impact elements
- Use Flask-Caching for frequently accessed data

## 6. 🧪 Testing & Quality Assurance

- Unit test all critical business logic
- Implement functional tests for UI components
- Use test data factories instead of live data
- Ensure tests are automated and repeatable

## 7. 🧭 Naming Conventions

- Python: snake_case for variables, functions, files, and directories
- JavaScript: camelCase for variables and functions
- Constants: UPPER_SNAKE_CASE
- Classes: PascalCase
- CSS: kebab-case for classes and IDs
- Files: descriptive, lowercase with appropriate extension

## 8. 🛠️ Configuration & Environment

- Store configuration in `.env` files as shown in pagination refactoring
- Separate development, staging, and production configurations
- Never hardcode credentials or secrets
- Version control configurations (except sensitive ones)

## 9. 📚 Documentation & Comments

- Document complex workflows inline or in dedicated files
- Use appropriate comment standards (JSDoc for JS, docstrings for Python)
- Maintain clear README files and setup guides
- Document architectural decisions (like in `PAGINATION_REFACTORING.md`)

## 10. 💾 Version Control & Commit Practices

- Use semantic commit messages:
  - feat: (new feature)
  - fix: (bug fix)
  - refactor: (code change that neither fixes a bug nor adds a feature)
  - docs: (documentation changes)
  - style: (formatting, missing semi-colons, etc; no code change)
  - test: (adding or refactoring tests; no production code change)
  - chore: (updating build tasks, package manager configs, etc)
- Create feature branches and use pull requests for review

## 11. 🧩 UI & UX Guidelines

- Use Tailwind CSS for consistent styling
- Implement responsive design principles
- Optimize theme transitions as shown in `theme-transition.css`
- Keep components minimal and reusable
- Follow accessibility best practices
- Implement proper modal and sidebar behaviors from existing CSS