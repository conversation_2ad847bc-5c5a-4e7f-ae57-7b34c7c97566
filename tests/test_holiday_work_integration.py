"""
Integration tests for holiday work workflows
"""

import pytest
import json
from datetime import date
from app import create_app, db
from app.models.attendance import Holiday, AttendanceRecord, AttendanceType
from app.models.employee import EmployeeDetail
from app.models.user import User


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['WTF_CSRF_ENABLED'] = False  # Disable CSRF for testing

    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def auth_user(app):
    """Create authenticated user for testing."""
    with app.app_context():
        # Create admin user
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='Admin'
        )
        admin_user.set_password('password')
        db.session.add(admin_user)

        # Create regular user
        user = User(
            username='testuser',
            email='<EMAIL>',
            role='User'
        )
        user.set_password('password')
        db.session.add(user)

        # Create employee
        employee = EmployeeDetail(
            user_id=2,  # Link to regular user
            employee_number='EMP001',
            first_name='John',
            last_name='Doe',
            emp_status='Active'
        )
        db.session.add(employee)

        # Create attendance type
        attendance_type = AttendanceType(
            name='Work From Home',
            code='WFH',
            description='Work from home',
            is_full_day=True,
            requires_approval=True
        )
        db.session.add(attendance_type)

        # Create holiday
        holiday = Holiday(
            name='Independence Day',
            date=date(2025, 6, 1),
            region_code='PH',
            description='Philippine Independence Day'
        )
        db.session.add(holiday)

        db.session.commit()

        return {
            'admin_user': admin_user,
            'user': user,
            'employee': employee,
            'attendance_type': attendance_type,
            'holiday': holiday
        }


class TestHolidayWorkIntegration:
    """Integration tests for holiday work workflows."""

    def login_user(self, client, username='admin', password='password'):
        """Helper method to login user."""
        return client.post('/auth/login', data={
            'username': username,
            'password': password
        }, follow_redirects=True)

    def test_admin_create_holiday_work_record(self, client, auth_user):
        """Test admin creating holiday work record through web interface."""
        # Login as admin
        self.login_user(client, 'admin')

        # Create attendance record on holiday
        response = client.post('/admin/attendance/records/create', data={
            'employee_detail_id': 1,
            'attendance_type_id': 1,
            'date': '2025-06-01',
            'status': 'Pending',
            'is_holiday_work': True,
            'holiday_work_reason': 'Emergency work on Independence Day',
            'notes': 'Critical project deadline'
        }, follow_redirects=True)

        assert response.status_code == 200

        # Verify record was created
        with client.application.app_context():
            record = AttendanceRecord.query.filter_by(date=date(2025, 6, 1)).first()
            assert record is not None
            assert record.is_holiday_work is True
            assert record.holiday_work_reason == 'Emergency work on Independence Day'

    def test_user_create_holiday_work_request(self, client, auth_user):
        """Test user creating holiday work request."""
        # Login as regular user
        self.login_user(client, 'testuser')

        # Create attendance request on holiday
        response = client.post('/attendance/request', data={
            'attendance_type_id': 1,
            'date': '2025-06-01',
            'is_holiday_work': True,
            'holiday_work_reason': 'Project deadline requires holiday work',
            'notes': 'Will work from home on Independence Day'
        }, follow_redirects=True)

        assert response.status_code == 200

        # Verify record was created with pending status
        with client.application.app_context():
            record = AttendanceRecord.query.filter_by(
                employee_detail_id=1,
                date=date(2025, 6, 1)
            ).first()
            assert record is not None
            assert record.is_holiday_work is True
            assert record.status == 'Pending'

    def test_holiday_work_approval_workflow(self, client, auth_user):
        """Test complete holiday work approval workflow."""
        with client.application.app_context():
            # Create a pending holiday work record
            record = AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 6, 1),
                status='Pending',
                is_holiday_work=True,
                holiday_work_reason='Emergency project work'
            )
            db.session.add(record)
            db.session.commit()
            record_id = record.id

        # Login as admin
        self.login_user(client, 'admin')

        # Approve the record
        response = client.post(f'/admin/attendance/records/{record_id}/approve', data={
            'notes': 'Approved due to project urgency'
        }, follow_redirects=True)

        assert response.status_code == 200

        # Verify approval
        with client.application.app_context():
            approved_record = AttendanceRecord.query.get(record_id)
            assert approved_record.status == 'Approved'
            assert approved_record.approved_by_id == 1  # Admin user ID

    def test_holiday_work_rejection_workflow(self, client, auth_user):
        """Test holiday work rejection workflow."""
        with client.application.app_context():
            # Create a pending holiday work record
            record = AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 6, 1),
                status='Pending',
                is_holiday_work=True,
                holiday_work_reason='Non-essential work'
            )
            db.session.add(record)
            db.session.commit()
            record_id = record.id

        # Login as admin
        self.login_user(client, 'admin')

        # Reject the record
        response = client.post(f'/admin/attendance/records/{record_id}/reject', data={
            'rejection_reason': 'Holiday work not justified for non-essential tasks'
        }, follow_redirects=True)

        assert response.status_code == 200

        # Verify rejection
        with client.application.app_context():
            rejected_record = AttendanceRecord.query.get(record_id)
            assert rejected_record.status == 'Rejected'
            assert 'not justified' in rejected_record.rejection_reason

    def test_holiday_work_analytics_access(self, client, auth_user):
        """Test access to holiday work analytics."""
        # Create some test data
        with client.application.app_context():
            records = [
                AttendanceRecord(
                    employee_detail_id=1,
                    attendance_type_id=1,
                    date=date(2025, 6, 1),
                    status='Approved',
                    is_holiday_work=True,
                    holiday_work_reason='Project work'
                ),
                AttendanceRecord(
                    employee_detail_id=1,
                    attendance_type_id=1,
                    date=date(2025, 12, 25),
                    status='Pending',
                    is_holiday_work=True,
                    holiday_work_reason='Emergency support'
                )
            ]
            for record in records:
                db.session.add(record)
            db.session.commit()

        # Login as admin
        self.login_user(client, 'admin')

        # Access analytics page
        response = client.get('/admin/holiday-work/analytics')
        assert response.status_code == 200

        # Check that analytics data is present
        assert b'Holiday Work Analytics' in response.data

    def test_holiday_detection_api(self, client, auth_user):
        """Test holiday detection API endpoint."""
        # Login as admin
        self.login_user(client, 'admin')

        # Test holiday detection for Independence Day
        response = client.get('/api/holidays/check/2025-06-01/1')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['is_holiday'] is True
        assert data['holiday_name'] == 'Independence Day'
        assert data['region_code'] == 'PH'

        # Test non-holiday date
        response = client.get('/api/holidays/check/2025-05-15/1')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['is_holiday'] is False

    def test_holiday_work_filtering(self, client, auth_user):
        """Test filtering of holiday work records."""
        # Create test data
        with client.application.app_context():
            records = [
                AttendanceRecord(
                    employee_detail_id=1,
                    attendance_type_id=1,
                    date=date(2025, 6, 1),
                    status='Approved',
                    is_holiday_work=True,
                    holiday_work_reason='Holiday work 1'
                ),
                AttendanceRecord(
                    employee_detail_id=1,
                    attendance_type_id=1,
                    date=date(2025, 5, 15),
                    status='Approved',
                    is_holiday_work=False
                )
            ]
            for record in records:
                db.session.add(record)
            db.session.commit()

        # Login as admin
        self.login_user(client, 'admin')

        # Test filtering for holiday work only
        response = client.get('/admin/attendance/records?holiday_work=true')
        assert response.status_code == 200

        # Should show only holiday work records
        assert b'Holiday work 1' in response.data

    def test_user_access_restrictions(self, client, auth_user):
        """Test that regular users cannot access admin holiday work features."""
        # Login as regular user
        self.login_user(client, 'testuser')

        # Try to access admin analytics
        response = client.get('/admin/holiday-work/analytics')
        assert response.status_code == 403 or response.status_code == 302

        # Try to access admin attendance records
        response = client.get('/admin/attendance/records')
        assert response.status_code == 403 or response.status_code == 302


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
