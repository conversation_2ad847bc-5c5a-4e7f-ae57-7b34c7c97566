"""
Unit tests for HolidayWorkAnalytics service
"""

import pytest
from datetime import date, datetime, timedelta
from app import create_app, db
from app.models.attendance import AttendanceRecord, AttendanceType, Holiday
from app.models.employee import EmployeeDetail
from app.models.user import User
from app.services.holiday_work_analytics import HolidayWorkAnalytics


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def sample_data(app):
    """Create sample data for testing."""
    with app.app_context():
        # Create user
        user = User(
            username='testuser',
            email='<EMAIL>',
            role='User'
        )
        user.set_password('password')
        db.session.add(user)
        
        # Create employee
        employee = EmployeeDetail(
            user_id=1,
            employee_number='EMP001',
            first_name='<PERSON>',
            last_name='Doe',
            emp_status='Active'
        )
        db.session.add(employee)
        
        # Create attendance type
        attendance_type = AttendanceType(
            name='Work From Home',
            code='WFH',
            description='Work from home',
            is_full_day=True,
            requires_approval=True
        )
        db.session.add(attendance_type)
        
        # Create holidays
        holidays = [
            Holiday(
                name='Independence Day',
                date=date(2025, 6, 1),
                region_code='PH',
                description='Philippine Independence Day'
            ),
            Holiday(
                name='Christmas Day',
                date=date(2025, 12, 25),
                region_code='PH',
                description='Christmas Day'
            )
        ]
        for holiday in holidays:
            db.session.add(holiday)
        
        db.session.commit()
        
        # Create attendance records
        records = [
            # Holiday work records
            AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 6, 1),
                status='Approved',
                is_holiday_work=True,
                holiday_work_reason='Emergency work on Independence Day'
            ),
            AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 12, 25),
                status='Pending',
                is_holiday_work=True,
                holiday_work_reason='Project deadline on Christmas'
            ),
            # Regular records
            AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 5, 15),
                status='Approved',
                is_holiday_work=False
            )
        ]
        
        for record in records:
            db.session.add(record)
        
        db.session.commit()
        
        return {
            'user': user,
            'employee': employee,
            'attendance_type': attendance_type,
            'holidays': holidays,
            'records': records
        }


class TestHolidayWorkAnalytics:
    """Test cases for HolidayWorkAnalytics service."""
    
    def test_get_overview_stats(self, app, sample_data):
        """Test overview statistics calculation."""
        with app.app_context():
            analytics = HolidayWorkAnalytics()
            stats = analytics.get_overview_stats()
            
            assert stats['total_holiday_work'] == 2
            assert stats['approved_holiday_work'] == 1
            assert stats['pending_holiday_work'] == 1
            assert stats['rejected_holiday_work'] == 0
            assert stats['unique_employees'] == 1
            assert stats['approval_rate'] == 50.0
    
    def test_get_employee_rankings(self, app, sample_data):
        """Test employee rankings calculation."""
        with app.app_context():
            analytics = HolidayWorkAnalytics()
            rankings = analytics.get_employee_rankings()
            
            assert 'rankings' in rankings
            assert 'total_employees' in rankings
            assert rankings['total_employees'] == 1
            
            employee_ranking = rankings['rankings'][0]
            assert employee_ranking['employee_name'] == 'John Doe'
            assert employee_ranking['holiday_work_count'] == 2
            assert employee_ranking['approval_rate'] == 50.0
            assert employee_ranking['rank'] == 1
    
    def test_get_holiday_breakdown(self, app, sample_data):
        """Test holiday breakdown calculation."""
        with app.app_context():
            analytics = HolidayWorkAnalytics()
            breakdown = analytics.get_holiday_breakdown()
            
            assert 'holiday_breakdown' in breakdown
            assert 'total_holidays_with_work' in breakdown
            assert breakdown['total_holidays_with_work'] == 2
            
            # Check Independence Day breakdown
            independence_day = next(
                (h for h in breakdown['holiday_breakdown'] 
                 if h['holiday_name'] == 'Independence Day'), 
                None
            )
            assert independence_day is not None
            assert independence_day['work_count'] == 1
            assert independence_day['approval_rate'] == 100.0
    
    def test_get_monthly_trends(self, app, sample_data):
        """Test monthly trends calculation."""
        with app.app_context():
            analytics = HolidayWorkAnalytics()
            trends = analytics.get_monthly_trends(2025)
            
            assert 'monthly_trends' in trends
            assert 'total_year' in trends
            assert 'peak_month' in trends
            assert trends['total_year'] == 2
            
            # Check June data (Independence Day)
            june_data = next(
                (m for m in trends['monthly_trends'] if m['month'] == 6), 
                None
            )
            assert june_data is not None
            assert june_data['total'] == 1
            assert june_data['approved'] == 1
    
    def test_get_dashboard_summary(self, app, sample_data):
        """Test dashboard summary calculation."""
        with app.app_context():
            analytics = HolidayWorkAnalytics()
            summary = analytics.get_dashboard_summary()
            
            assert 'recent_period' in summary
            assert 'year_to_date' in summary
            assert 'upcoming_holidays' in summary
            
            # Check recent period stats
            recent_stats = summary['recent_period']['stats']
            assert 'total_holiday_work' in recent_stats
            assert 'approval_rate' in recent_stats
            
            # Check year to date
            ytd_stats = summary['year_to_date']
            assert ytd_stats['total_holiday_work'] == 2
    
    def test_empty_data_handling(self, app):
        """Test analytics with no data."""
        with app.app_context():
            analytics = HolidayWorkAnalytics()
            
            # Test overview with no data
            stats = analytics.get_overview_stats()
            assert stats['total_holiday_work'] == 0
            assert stats['approval_rate'] == 0.0
            
            # Test rankings with no data
            rankings = analytics.get_employee_rankings()
            assert rankings['total_employees'] == 0
            assert len(rankings['rankings']) == 0
            
            # Test breakdown with no data
            breakdown = analytics.get_holiday_breakdown()
            assert breakdown['total_holidays_with_work'] == 0
            assert len(breakdown['holiday_breakdown']) == 0


if __name__ == '__main__':
    pytest.main([__file__])
