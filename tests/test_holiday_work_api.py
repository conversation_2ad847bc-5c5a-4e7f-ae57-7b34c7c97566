"""
API endpoint tests for holiday work features
"""

import pytest
import json
from datetime import date, datetime
from app import create_app, db
from app.models.attendance import Holiday, AttendanceRecord, AttendanceType
from app.models.employee import EmployeeDetail
from app.models.user import User


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['WTF_CSRF_ENABLED'] = False
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def auth_data(app):
    """Create authentication data for testing."""
    with app.app_context():
        # Create admin user
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='Admin'
        )
        admin_user.set_password('password')
        db.session.add(admin_user)
        
        # Create employee
        employee = EmployeeDetail(
            user_id=1,
            employee_number='EMP001',
            first_name='<PERSON>',
            last_name='Doe',
            emp_status='Active'
        )
        db.session.add(employee)
        
        # Create attendance type
        attendance_type = AttendanceType(
            name='Work From Home',
            code='WFH',
            description='Work from home',
            is_full_day=True,
            requires_approval=True
        )
        db.session.add(attendance_type)
        
        # Create holidays
        holidays = [
            Holiday(
                name='Independence Day',
                date=date(2025, 6, 1),
                region_code='PH',
                description='Philippine Independence Day'
            ),
            Holiday(
                name='Christmas Day',
                date=date(2025, 12, 25),
                region_code='PH',
                description='Christmas Day'
            ),
            Holiday(
                name='New Year',
                date=date(2025, 1, 1),
                region_code='GLOBAL',
                description='Global New Year'
            )
        ]
        
        for holiday in holidays:
            db.session.add(holiday)
        
        # Create attendance records
        records = [
            AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 6, 1),
                status='Approved',
                is_holiday_work=True,
                holiday_work_reason='Emergency project work'
            ),
            AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 12, 25),
                status='Pending',
                is_holiday_work=True,
                holiday_work_reason='Year-end support'
            ),
            AttendanceRecord(
                employee_detail_id=1,
                attendance_type_id=1,
                date=date(2025, 5, 15),
                status='Approved',
                is_holiday_work=False
            )
        ]
        
        for record in records:
            db.session.add(record)
        
        db.session.commit()
        
        return {
            'admin_user': admin_user,
            'employee': employee,
            'attendance_type': attendance_type,
            'holidays': holidays,
            'records': records
        }


class TestHolidayWorkAPI:
    """Test cases for holiday work API endpoints."""
    
    def login_user(self, client, username='admin', password='password'):
        """Helper method to login user."""
        return client.post('/auth/login', data={
            'username': username,
            'password': password
        }, follow_redirects=True)
    
    def test_holiday_check_api(self, client, auth_data):
        """Test holiday check API endpoint."""
        self.login_user(client)
        
        # Test holiday date
        response = client.get('/api/holidays/check/2025-06-01/1')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['is_holiday'] is True
        assert data['holiday_name'] == 'Independence Day'
        assert data['region_code'] == 'PH'
        assert 'holiday_id' in data
        
        # Test non-holiday date
        response = client.get('/api/holidays/check/2025-05-15/1')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['is_holiday'] is False
        assert data['region_code'] == 'PH'
    
    def test_holiday_check_api_invalid_date(self, client, auth_data):
        """Test holiday check API with invalid date."""
        self.login_user(client)
        
        response = client.get('/api/holidays/check/invalid-date/1')
        assert response.status_code == 400
    
    def test_holiday_check_api_invalid_employee(self, client, auth_data):
        """Test holiday check API with invalid employee."""
        self.login_user(client)
        
        response = client.get('/api/holidays/check/2025-06-01/999')
        assert response.status_code == 404
    
    def test_holiday_work_analytics_api(self, client, auth_data):
        """Test holiday work analytics API endpoint."""
        self.login_user(client)
        
        response = client.get('/api/holiday-work/analytics/overview')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'total_holiday_work' in data
        assert 'approved_holiday_work' in data
        assert 'pending_holiday_work' in data
        assert 'approval_rate' in data
        
        assert data['total_holiday_work'] == 2
        assert data['approved_holiday_work'] == 1
        assert data['pending_holiday_work'] == 1
    
    def test_holiday_work_employee_rankings_api(self, client, auth_data):
        """Test holiday work employee rankings API."""
        self.login_user(client)
        
        response = client.get('/api/holiday-work/analytics/employee-rankings')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'rankings' in data
        assert 'total_employees' in data
        
        assert data['total_employees'] == 1
        assert len(data['rankings']) == 1
        
        employee_data = data['rankings'][0]
        assert employee_data['employee_name'] == 'John Doe'
        assert employee_data['holiday_work_count'] == 2
        assert employee_data['rank'] == 1
    
    def test_holiday_work_breakdown_api(self, client, auth_data):
        """Test holiday work breakdown API."""
        self.login_user(client)
        
        response = client.get('/api/holiday-work/analytics/holiday-breakdown')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'holiday_breakdown' in data
        assert 'total_holidays_with_work' in data
        
        assert data['total_holidays_with_work'] == 2
        assert len(data['holiday_breakdown']) == 2
    
    def test_holiday_work_monthly_trends_api(self, client, auth_data):
        """Test holiday work monthly trends API."""
        self.login_user(client)
        
        response = client.get('/api/holiday-work/analytics/monthly-trends/2025')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'monthly_trends' in data
        assert 'total_year' in data
        assert 'peak_month' in data
        
        assert data['total_year'] == 2
        assert len(data['monthly_trends']) == 12  # All 12 months
    
    def test_holiday_work_dashboard_api(self, client, auth_data):
        """Test holiday work dashboard API."""
        self.login_user(client)
        
        response = client.get('/api/holiday-work/analytics/dashboard')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'recent_period' in data
        assert 'year_to_date' in data
        assert 'upcoming_holidays' in data
    
    def test_attendance_records_api_holiday_filter(self, client, auth_data):
        """Test attendance records API with holiday work filter."""
        self.login_user(client)
        
        # Test filtering for holiday work only
        response = client.get('/api/attendance/records?holiday_work=true')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'records' in data
        assert 'total' in data
        
        # Should return only holiday work records
        assert data['total'] == 2
        for record in data['records']:
            assert record['is_holiday_work'] is True
    
    def test_attendance_records_api_date_filter(self, client, auth_data):
        """Test attendance records API with date filter."""
        self.login_user(client)
        
        # Test filtering by date range
        response = client.get('/api/attendance/records?start_date=2025-06-01&end_date=2025-06-01')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['total'] == 1
        assert data['records'][0]['date'] == '2025-06-01'
    
    def test_attendance_records_api_status_filter(self, client, auth_data):
        """Test attendance records API with status filter."""
        self.login_user(client)
        
        # Test filtering by status
        response = client.get('/api/attendance/records?status=Approved')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['total'] == 2  # 1 holiday work + 1 regular approved
        
        for record in data['records']:
            assert record['status'] == 'Approved'
    
    def test_api_authentication_required(self, client, auth_data):
        """Test that API endpoints require authentication."""
        # Test without login
        response = client.get('/api/holiday-work/analytics/overview')
        assert response.status_code == 302 or response.status_code == 401
        
        response = client.get('/api/holidays/check/2025-06-01/1')
        assert response.status_code == 302 or response.status_code == 401
    
    def test_api_pagination(self, client, auth_data):
        """Test API pagination for attendance records."""
        self.login_user(client)
        
        response = client.get('/api/attendance/records?page=1&per_page=2')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'records' in data
        assert 'total' in data
        assert 'page' in data
        assert 'per_page' in data
        assert 'total_pages' in data
        
        assert data['page'] == 1
        assert data['per_page'] == 2
        assert len(data['records']) <= 2
    
    def test_api_error_handling(self, client, auth_data):
        """Test API error handling."""
        self.login_user(client)
        
        # Test invalid year for monthly trends
        response = client.get('/api/holiday-work/analytics/monthly-trends/invalid')
        assert response.status_code == 400
        
        # Test invalid date format
        response = client.get('/api/attendance/records?start_date=invalid-date')
        assert response.status_code == 400


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
