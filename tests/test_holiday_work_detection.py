"""
Unit tests for holiday work detection logic
"""

import pytest
from datetime import date
from app import create_app, db
from app.models.attendance import Holiday, AttendanceRecord, AttendanceType
from app.models.employee import EmployeeDetail
from app.models.user import User
from app.services.attendance_service import AttendanceService
from app.services.holiday_service import HolidayService


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def sample_data(app):
    """Create sample data for testing."""
    with app.app_context():
        # Create user and employee
        user = User(
            username='testuser',
            email='<EMAIL>',
            role='User'
        )
        user.set_password('password')
        db.session.add(user)
        
        employee = EmployeeDetail(
            user_id=1,
            employee_number='EMP001',
            first_name='John',
            last_name='Doe',
            emp_status='Active'
        )
        db.session.add(employee)
        
        # Create attendance type
        attendance_type = AttendanceType(
            name='Work From Home',
            code='WFH',
            description='Work from home',
            is_full_day=True,
            requires_approval=True
        )
        db.session.add(attendance_type)
        
        # Create holidays for different regions
        holidays = [
            Holiday(
                name='Independence Day',
                date=date(2025, 6, 1),
                region_code='PH',
                description='Philippine Independence Day'
            ),
            Holiday(
                name='Independence Day',
                date=date(2025, 7, 4),
                region_code='US',
                description='US Independence Day'
            ),
            Holiday(
                name='New Year',
                date=date(2025, 1, 1),
                region_code='GLOBAL',
                description='Global New Year'
            )
        ]
        
        for holiday in holidays:
            db.session.add(holiday)
        
        db.session.commit()
        
        return {
            'user': user,
            'employee': employee,
            'attendance_type': attendance_type,
            'holidays': holidays
        }


class TestHolidayWorkDetection:
    """Test cases for holiday work detection logic."""
    
    def test_check_holiday_date_ph_holiday(self, app, sample_data):
        """Test holiday detection for Philippine holiday."""
        with app.app_context():
            # Test Philippine Independence Day
            holiday_info = AttendanceService.check_holiday_date(1, date(2025, 6, 1))
            
            assert holiday_info['is_holiday'] is True
            assert holiday_info['holiday_name'] == 'Independence Day'
            assert holiday_info['region_code'] == 'PH'
            assert 'holiday_id' in holiday_info
    
    def test_check_holiday_date_us_holiday(self, app, sample_data):
        """Test holiday detection for US holiday."""
        with app.app_context():
            # Test US Independence Day
            holiday_info = AttendanceService.check_holiday_date(1, date(2025, 7, 4))
            
            # Should detect PH region by default, so US holiday should not be detected
            assert holiday_info['is_holiday'] is False
    
    def test_check_holiday_date_global_holiday(self, app, sample_data):
        """Test holiday detection for global holiday."""
        with app.app_context():
            # Test Global New Year
            holiday_info = AttendanceService.check_holiday_date(1, date(2025, 1, 1))
            
            assert holiday_info['is_holiday'] is True
            assert holiday_info['holiday_name'] == 'New Year'
            assert holiday_info['region_code'] == 'GLOBAL'
    
    def test_check_holiday_date_non_holiday(self, app, sample_data):
        """Test holiday detection for non-holiday date."""
        with app.app_context():
            # Test regular date
            holiday_info = AttendanceService.check_holiday_date(1, date(2025, 5, 15))
            
            assert holiday_info['is_holiday'] is False
            assert holiday_info['region_code'] == 'PH'  # Default region
    
    def test_holiday_service_is_holiday(self, app, sample_data):
        """Test HolidayService.is_holiday method."""
        with app.app_context():
            # Test PH holiday
            assert HolidayService.is_holiday(date(2025, 6, 1), 'PH') is True
            assert HolidayService.is_holiday(date(2025, 7, 4), 'PH') is False
            
            # Test US holiday
            assert HolidayService.is_holiday(date(2025, 7, 4), 'US') is True
            assert HolidayService.is_holiday(date(2025, 6, 1), 'US') is False
            
            # Test Global holiday
            assert HolidayService.is_holiday(date(2025, 1, 1), 'PH') is True
            assert HolidayService.is_holiday(date(2025, 1, 1), 'US') is True
            assert HolidayService.is_holiday(date(2025, 1, 1), 'GLOBAL') is True
    
    def test_create_attendance_record_with_holiday_detection(self, app, sample_data):
        """Test attendance record creation with automatic holiday detection."""
        with app.app_context():
            # Create record on holiday date without manual flag
            result = AttendanceService.create_attendance_record(
                employee_detail_id=1,
                attendance_type_id=1,
                record_date=date(2025, 6, 1),
                notes='Test record on holiday',
                status='Pending'
            )
            
            assert result['success'] is True
            assert result['is_holiday_work'] is True
            assert result['holiday_info']['is_holiday'] is True
            
            record = result['record']
            assert record.is_holiday_work is True
    
    def test_create_attendance_record_manual_override(self, app, sample_data):
        """Test attendance record creation with manual holiday work override."""
        with app.app_context():
            # Create record on non-holiday date with manual holiday work flag
            result = AttendanceService.create_attendance_record(
                employee_detail_id=1,
                attendance_type_id=1,
                record_date=date(2025, 5, 15),
                notes='Test manual holiday work',
                status='Pending',
                is_holiday_work=True,
                holiday_work_reason='Manual override for testing'
            )
            
            assert result['success'] is True
            assert result['is_holiday_work'] is True
            
            record = result['record']
            assert record.is_holiday_work is True
            assert record.holiday_work_reason == 'Manual override for testing'
    
    def test_update_attendance_record_holiday_work(self, app, sample_data):
        """Test updating attendance record with holiday work."""
        with app.app_context():
            # First create a regular record
            result = AttendanceService.create_attendance_record(
                employee_detail_id=1,
                attendance_type_id=1,
                record_date=date(2025, 5, 15),
                notes='Regular record',
                status='Pending'
            )
            
            record_id = result['record'].id
            
            # Update to add holiday work
            update_result = AttendanceService.update_attendance_record(
                record_id=record_id,
                is_holiday_work=True,
                holiday_work_reason='Updated to holiday work'
            )
            
            assert update_result['success'] is True
            
            # Verify update
            updated_record = AttendanceRecord.query.get(record_id)
            assert updated_record.is_holiday_work is True
            assert updated_record.holiday_work_reason == 'Updated to holiday work'
    
    def test_weekend_holiday_detection(self, app, sample_data):
        """Test holiday detection works for holidays on weekends."""
        with app.app_context():
            # June 1, 2025 is a Sunday
            holiday_info = AttendanceService.check_holiday_date(1, date(2025, 6, 1))
            
            assert holiday_info['is_holiday'] is True
            assert holiday_info['holiday_name'] == 'Independence Day'
            
            # Verify the date is actually a Sunday
            assert date(2025, 6, 1).weekday() == 6  # Sunday = 6
    
    def test_get_employee_region(self, app, sample_data):
        """Test employee region detection."""
        with app.app_context():
            region = AttendanceService.get_employee_region(1)
            
            # Should default to PH
            assert region == 'PH'


if __name__ == '__main__':
    pytest.main([__file__])
