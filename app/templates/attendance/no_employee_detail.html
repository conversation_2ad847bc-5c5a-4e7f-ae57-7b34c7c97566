{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
  <!-- Page Header -->
  <div class="text-center">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
  </div>

  <!-- Message Card -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Employee Details Required</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        You need to have employee details set up before you can manage attendance records.
      </p>
      <div class="mt-6">
        <a href="{{ url_for('main.view_my_details') }}" class="btn btn-primary btn-md">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          View My Profile
        </a>
      </div>
      <div class="mt-4">
        <p class="text-xs text-gray-500 dark:text-gray-400">
          If you believe this is an error, please contact your administrator.
        </p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
