{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}
{% from "components/button.html" import button, button_group, icon_button %}
{% from "partials/forms/base_form.html" import form_group %}

{% block title %}Attendance Records{% endblock %}

{% block header %}Attendance Records{% endblock %}

{% block content %}
<!-- Hidden fields to store URLs for JavaScript use -->
<input type="hidden" id="add-attendance-record-url" data-url="{{ url_for('admin_attendance.get_attendance_record_form_create') }}">
<input type="hidden" id="edit-attendance-record-base-url" data-url="{{ url_for('admin_attendance.get_attendance_record_form_edit', record_id=0) }}">
<input type="hidden" id="delete-attendance-record-base-url" data-url="{{ url_for('admin_attendance.delete_attendance_record', record_id=0) }}">
<input type="hidden" id="approve-attendance-record-base-url" data-url="{{ url_for('admin_attendance.approve_attendance_record', record_id=0) }}">
<input type="hidden" id="reject-attendance-record-base-url" data-url="{{ url_for('admin_attendance.reject_attendance_record', record_id=0) }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="Attendance Records",
    button_text="Add Attendance Record",
    button_icon="plus",
    button_action="openAddAttendanceRecordForm()",
    description="Manage employee attendance records and track work schedules."
  ) }}

  <!-- Filter Section -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Filter Attendance Records</h3>
    </div>
    <div class="card-content">
      <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <!-- Employee Filter -->
          {% set employee_options = filter_form.employee.choices %}
          {{ form_group(
            label="Employee",
            name="employee",
            type="select",
            value=request.args.get('employee', ''),
            options=employee_options
          ) }}

          <!-- Attendance Type Filter -->
          {% set attendance_type_options = filter_form.attendance_type.choices %}
          {{ form_group(
            label="Attendance Type",
            name="attendance_type",
            type="select",
            value=request.args.get('attendance_type', ''),
            options=attendance_type_options
          ) }}

          <!-- Status Filter -->
          {% set status_options = filter_form.status.choices %}
          {{ form_group(
            label="Status",
            name="status",
            type="select",
            value=request.args.get('status', ''),
            options=status_options
          ) }}

          <!-- Holiday Work Filter -->
          {% set holiday_work_options = filter_form.is_holiday_work.choices %}
          {{ form_group(
            label="Holiday Work",
            name="is_holiday_work",
            type="select",
            value=request.args.get('is_holiday_work', ''),
            options=holiday_work_options
          ) }}

          <!-- Start Date Filter -->
          {{ form_group(
            label="Start Date",
            name="start_date",
            type="date",
            value=request.args.get('start_date', '')
          ) }}

          <!-- End Date Filter -->
          {{ form_group(
            label="End Date",
            name="end_date",
            type="date",
            value=request.args.get('end_date', '')
          ) }}
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-end gap-2">
          {% set filter_buttons = [
            {"text": "Reset", "variant": "outline", "href": url_for('admin_attendance.list_attendance_records'), "icon": "rotate-ccw"},
            {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
          ] %}
          {{ button_group(filter_buttons) }}
        </div>
      </form>
    </div>
  </div>

  <!-- Quick Actions -->
  {% set quick_action_buttons = [
    {"text": "Export", "variant": "outline", "onclick": "exportAttendanceRecords()", "icon": "download"}
  ] %}
  {{ button_group(quick_action_buttons) }}

  <!-- Attendance Records Table -->
  <div class="card">
    {{ table_header(
      title="Attendance Records List",
      count=total_count,
      count_label="records"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Employee'},
        {'label': 'Date', 'sortable': True},
        {'label': 'Type'},
        {'label': 'Duration'},
        {'label': 'Status'},
        {'label': 'Holiday'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=attendance_records,
      empty_icon="calendar-clock",
      empty_title="No attendance records found",
      empty_description="Create your first attendance record or adjust your filters.",
      empty_button_text="Add Attendance Record",
      empty_button_icon="plus",
      empty_button_action="openAddAttendanceRecordForm()"
    ) %}
      {% for record in attendance_records %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-8 w-8">
              <div class="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                <span class="text-sm font-medium">
                  {{ record.employee_detail.first_name[0] }}{{ record.employee_detail.last_name[0] }}
                </span>
              </div>
            </div>
            <div class="ml-3">
              <div class="font-medium">
                {{ record.employee_detail.first_name }} {{ record.employee_detail.last_name }}
              </div>
              <div class="text-xs text-muted-foreground">
                {{ record.employee_detail.employee_number }}
              </div>
            </div>
          </div>
        </td>
        <td class="px-4 py-3 text-sm font-medium">
          <div class="flex items-center">
            <i data-lucide="calendar" class="w-4 h-4 mr-2 text-muted-foreground"></i>
            {{ record.date.strftime('%Y-%m-%d') }}
            <span class="ml-2 text-xs text-muted-foreground">
              ({{ record.date.strftime('%A') }})
            </span>
          </div>
        </td>
        <td class="px-4 py-3 text-sm">
          <div class="font-medium">{{ record.attendance_type.name }}</div>
          <div class="text-xs text-muted-foreground">{{ record.attendance_type.code }}</div>
        </td>
        <td class="px-4 py-3 text-sm">
          {% if record.attendance_type.is_full_day %}
            <span class="badge badge-info">Full Day</span>
          {% else %}
            {% if record.duration_hours %}
              <div class="font-medium">{{ record.duration_hours }}h</div>
            {% endif %}
            {% if record.start_time and record.end_time %}
              <div class="text-xs text-muted-foreground">
                {{ record.start_time.strftime('%H:%M') }} - {{ record.end_time.strftime('%H:%M') }}
              </div>
            {% endif %}
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm">
          {% if record.status == 'Pending' %}
            <span class="badge badge-warning">Pending</span>
          {% elif record.status == 'Approved' %}
            <span class="badge badge-success">Approved</span>
          {% elif record.status == 'Rejected' %}
            <span class="badge badge-destructive">Rejected</span>
          {% elif record.status == 'Auto-Approved' %}
            <span class="badge badge-info">Auto Approved</span>
          {% elif record.status == 'Cancelled' %}
            <span class="badge badge-secondary">Cancelled</span>
          {% else %}
            <span class="badge badge-secondary">{{ record.status }}</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm">
          {% if record.is_holiday_work %}
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-1 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
              <span class="badge badge-warning text-xs">Holiday Work</span>
            </div>
            {% if record.holiday_work_reason %}
              <div class="text-xs text-muted-foreground mt-1 max-w-32 truncate" title="{{ record.holiday_work_reason }}">
                {{ record.holiday_work_reason }}
              </div>
            {% endif %}
          {% else %}
            <span class="text-muted-foreground">-</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {% set escaped_employee_name = (record.employee_detail.first_name + ' ' + record.employee_detail.last_name) | replace("'", "\\'") | replace('"', '\\"') %}
          {% set actions = [
            {'type': 'button', 'action': "openEditAttendanceRecordForm(" ~ record.id ~ ")", 'icon': 'edit', 'title': 'Edit'}
          ] %}
          {% if record.status == 'Pending' %}
            {% set _ = actions.append({'type': 'button', 'action': "approveAttendanceRecord(" ~ record.id ~ ")", 'icon': 'check', 'variant': 'text-success', 'title': 'Approve'}) %}
            {% set _ = actions.append({'type': 'button', 'action': "rejectAttendanceRecord(" ~ record.id ~ ")", 'icon': 'x', 'variant': 'text-warning', 'title': 'Reject'}) %}
          {% endif %}
          {% set _ = actions.append({'type': 'button', 'action': "confirmDeleteAttendanceRecord(" ~ record.id ~ ", '" ~ escaped_employee_name ~ "', '" ~ record.date.strftime('%Y-%m-%d') ~ "')", 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}) %}
          <div class="flex justify-end space-x-2">
            {% for action in actions %}
              {{ icon_button(action.icon, variant="ghost", size="sm", onclick=action.action, title=action.title, class_extra=action.variant if action.variant else "") }}
            {% endfor %}
          </div>
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    <!-- Pagination -->
    {% include "partials/pagination.html" %}
  </div>
</div>


{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/pages/attendance-records.js') }}"></script>
{% endblock %}
