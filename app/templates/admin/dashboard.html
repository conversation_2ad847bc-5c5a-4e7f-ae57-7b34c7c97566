{% extends "base.html" %}

{% from "components/modal.html" import modal %}

{% block title %}Dashboard{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}
<!-- Mark this page as needing Chart.js -->
<div data-needs-chart hidden></div>
<!-- Dashboard Header -->
<div class="mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/10 dark:to-transparent">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome, {{ current_user.name }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1.5">Dashboard overview and key metrics</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 h-9 px-4 py-2 shadow-sm" aria-label="Refresh Dashboard" id="refresh-dashboard-btn">
                        <i data-lucide="refresh-cw" class="refresh-icon w-4 h-4 mr-2"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 h-9 px-4 py-2 shadow-sm" aria-label="View Analytics">
                        <i data-lucide="bar-chart-2" class="w-4 h-4 mr-2"></i>
                        <span>Analytics</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-5">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i data-lucide="activity" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
            <span>Key Metrics</span>
        </h2>
        <div class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 px-3 py-1 rounded-full">
            <span>Last updated: </span>
            <time datetime="{{ now }}" class="font-medium">{{ now.strftime('%b %d, %Y at %H:%M') }}</time>
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
        <!-- Users Stats Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-blue-50/30 to-transparent dark:from-blue-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ user_count }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800/30">Active</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="users" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ url_for('admin.users') }}" class="text-xs flex items-center text-blue-600 dark:text-blue-400 hover:underline font-medium">
                        <span>View all users</span>
                        <i data-lucide="chevron-right" class="w-3.5 h-3.5 ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Teams Stats Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-green-50/30 to-transparent dark:from-green-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Teams</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ team_count }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800/30">Active</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 shadow-sm">
                        <i data-lucide="users-round" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ url_for('teams.index') }}" class="text-xs flex items-center text-blue-600 dark:text-blue-400 hover:underline font-medium">
                        <span>Manage teams</span>
                        <i data-lucide="chevron-right" class="w-3.5 h-3.5 ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Business Units Stats Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-indigo-50/30 to-transparent dark:from-indigo-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Business Units</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ business_unit_count }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30">Operational</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 shadow-sm">
                        <i data-lucide="briefcase" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ url_for('admin.business_units') }}" class="text-xs flex items-center text-blue-600 dark:text-blue-400 hover:underline font-medium">
                        <span>Manage units</span>
                        <i data-lucide="chevron-right" class="w-3.5 h-3.5 ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Business Segments Stats Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-purple-50/30 to-transparent dark:from-purple-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Business Segments</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ business_segment_count }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800/30">Active</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 shadow-sm">
                        <i data-lucide="layers" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ url_for('admin.business_segments') }}" class="text-xs flex items-center text-blue-600 dark:text-blue-400 hover:underline font-medium">
                        <span>View segments</span>
                        <i data-lucide="chevron-right" class="w-3.5 h-3.5 ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Holiday Work Analytics Section -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-5">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <svg class="w-5 h-5 text-amber-600 dark:text-amber-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
            <span>Holiday Work Analytics</span>
        </h2>
        <a href="{{ url_for('admin.holiday_work_reports') }}" class="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center font-medium">
            View detailed reports
            <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
        <!-- Recent Holiday Work (30 days) -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-amber-50/30 to-transparent dark:from-amber-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Recent Holiday Work</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ holiday_work_summary.recent_period.stats.total_holiday_work }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 border border-amber-200 dark:border-amber-800/30">Last 30 days</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 shadow-sm">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-xs text-gray-600 dark:text-gray-400">
                        <span>Approval rate: {{ holiday_work_summary.recent_period.stats.approval_rate }}%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Year-to-Date Holiday Work -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-orange-50/30 to-transparent dark:from-orange-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Year-to-Date</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ holiday_work_summary.year_to_date.total_holiday_work }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30">{{ now.year }}</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 shadow-sm">
                        <i data-lucide="calendar" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-xs text-gray-600 dark:text-gray-400">
                        <span>{{ holiday_work_summary.year_to_date.unique_employees }} employees involved</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Employee (Recent) -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-blue-50/30 to-transparent dark:from-blue-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Top Employee</p>
                        <div class="mt-2">
                            {% if holiday_work_summary.top_employees_recent %}
                                <p class="text-lg font-bold text-gray-900 dark:text-white">{{ holiday_work_summary.top_employees_recent[0].employee_name }}</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400">{{ holiday_work_summary.top_employees_recent[0].holiday_work_count }} records</p>
                            {% else %}
                                <p class="text-lg font-medium text-gray-500 dark:text-gray-400">No data</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="award" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-xs text-gray-600 dark:text-gray-400">
                        <span>Recent 30 days</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Holidays -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-purple-50/30 to-transparent dark:from-purple-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Next Holiday</p>
                        <div class="mt-2">
                            {% if holiday_work_summary.upcoming_holidays %}
                                <p class="text-lg font-bold text-gray-900 dark:text-white">{{ holiday_work_summary.upcoming_holidays[0].name }}</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400">In {{ holiday_work_summary.upcoming_holidays[0].days_until }} days</p>
                            {% else %}
                                <p class="text-lg font-medium text-gray-500 dark:text-gray-400">No upcoming</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 shadow-sm">
                        <i data-lucide="calendar-days" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-xs text-gray-600 dark:text-gray-400">
                        <span>Next 30 days</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Employee Distribution Chart -->
    <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <i data-lucide="bar-chart-2" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                        <span>Employee Distribution</span>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Staff allocation across business units</p>
                </div>
                <div class="flex items-center space-x-2 mt-3 sm:mt-0">
                    <div class="inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 p-1 text-xs bg-white dark:bg-gray-700 shadow-sm">
                        <button id="chartBarBtn" class="px-2.5 py-1.5 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-sm shadow-sm" aria-label="Bar Chart View">
                            <i data-lucide="bar-chart" class="w-3.5 h-3.5"></i>
                        </button>
                        <button id="chartLineBtn" class="px-2.5 py-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 rounded-sm" aria-label="Line Chart View">
                            <i data-lucide="line-chart" class="w-3.5 h-3.5"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-5 bg-white dark:bg-gray-800">
                <div class="h-80 w-full bg-white dark:bg-gray-800 rounded-md">
                    <canvas id="employeeDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Feed -->
    <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <i data-lucide="alert-triangle" class="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2"></i>
                        <span>Important Alerts</span>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Recent warnings and errors</p>
                </div>
                <a href="{{ url_for('admin.activities') }}" class="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center mt-2 sm:mt-0 font-medium">
                    View all
                    <i data-lucide="chevron-right" class="w-3.5 h-3.5 ml-1"></i>
                </a>
            </div>
            <div class="p-5 space-y-4">
                {% if recent_activities %}
                    <div class="relative pl-6 before:absolute before:left-2 before:top-0 before:h-full before:w-0.5 before:bg-orange-100 dark:before:bg-orange-900/20 space-y-4">
                        {% for activity in recent_activities %}
                        <div class="relative">
                            <!-- Activity dot indicator - color based on severity -->
                            <div class="absolute -left-6 top-1 w-4 h-4 rounded-full
                                {% if activity.severity == 'error' %}
                                    bg-red-100 dark:bg-red-900/30 border-2 border-red-500 dark:border-red-400
                                {% else %}
                                    bg-orange-100 dark:bg-orange-900/30 border-2 border-orange-500 dark:border-orange-400
                                {% endif %}
                                flex items-center justify-center shadow-sm">
                                <div class="w-1.5 h-1.5 rounded-full
                                    {% if activity.severity == 'error' %}
                                        bg-red-500 dark:bg-red-400
                                    {% else %}
                                        bg-orange-500 dark:bg-orange-400
                                    {% endif %}
                                "></div>
                            </div>

                            <!-- Activity content -->
                            <div class="group
                                {% if activity.severity == 'error' %}
                                    hover:bg-red-50/50 dark:hover:bg-red-900/10 border border-transparent hover:border-red-100 dark:hover:border-red-800/20
                                {% else %}
                                    hover:bg-orange-50/50 dark:hover:bg-orange-900/10 border border-transparent hover:border-orange-100 dark:hover:border-orange-800/20
                                {% endif %}
                                p-3 rounded-lg transition-colors">
                                <div class="flex items-start">
                                    <div>
                                        <div class="flex items-center gap-2">
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ activity.user.name }}</p>
                                            <span class="text-xs px-1.5 py-0.5 rounded-full
                                                {% if activity.severity == 'error' %}
                                                    bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800/30
                                                {% else %}
                                                    bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30
                                                {% endif %}
                                            ">
                                                {{ activity.severity | title }}
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ activity.action }}</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-1.5">
                                            <i data-lucide="clock" class="w-3 h-3
                                                {% if activity.severity == 'error' %}
                                                    text-red-500 dark:text-red-400
                                                {% else %}
                                                    text-orange-500 dark:text-orange-400
                                                {% endif %}
                                            "></i>
                                            <span>{{ activity.created_at.strftime('%b %d, %Y at %H:%M') }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="flex flex-col items-center justify-center py-8 text-center bg-gray-50 dark:bg-gray-700/20 rounded-lg border border-gray-100 dark:border-gray-700">
                        <div class="w-12 h-12 rounded-full bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/20 flex items-center justify-center mb-3 shadow-sm">
                            <i data-lucide="check-circle" class="w-6 h-6 text-green-500 dark:text-green-400"></i>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 text-sm font-medium">No warnings or errors found</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Important system events will appear here</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Initialize dashboard components
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard components
        initDashboard();
    });

    function initDashboard() {
        // Initialize chart
        initEmployeeDistributionChart();

        // Initialize refresh button
        const refreshBtn = document.getElementById('refresh-dashboard-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                // Show loading state
                this.classList.add('opacity-50', 'pointer-events-none');

                // Find the refresh icon and its SVG
                const refreshIcon = this.querySelector('.refresh-icon');
                const svg = refreshIcon ? refreshIcon.querySelector('svg') : null;
                if (svg) {
                    // Add spin animation directly to the SVG
                    svg.classList.add('animate-spin');
                    console.log('Added spin animation to refresh icon SVG');
                } else if (refreshIcon) {
                    // Fallback: add animation to the icon element itself
                    refreshIcon.classList.add('animate-spin');
                    console.log('Added spin animation to refresh icon element');
                } else {
                    console.log('Refresh icon not found in button');
                }

                // Actually refresh the page data
                setTimeout(() => {
                    // Reload the page to get fresh data
                    window.location.reload();
                }, 300);
            });
        }

        // Initialize analytics button
        const analyticsBtn = document.querySelector('button[aria-label="View Analytics"]');
        if (analyticsBtn) {
            analyticsBtn.addEventListener('click', function() {
                window.location.href = '{{ url_for("admin.analytics") }}';
            });
        }
    }

    function initEmployeeDistributionChart() {
        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.log('Chart.js not loaded yet, waiting...');
            setTimeout(initEmployeeDistributionChart, 100);
            return;
        }

        const ctx = document.getElementById('employeeDistributionChart').getContext('2d');

        // Get data from Flask
        const labels = {{ unit_labels|safe }};
        const data = {{ unit_data|safe }};

        // Generate color palette that works in both light and dark modes
        const colors = [];
        const borderColors = [];

        // Use a consistent color palette that works well in both modes
        const baseColors = [
            { light: 'rgba(59, 130, 246, 0.7)', dark: 'rgba(96, 165, 250, 0.7)', border: { light: 'rgb(37, 99, 235)', dark: 'rgb(59, 130, 246)' } },
            { light: 'rgba(16, 185, 129, 0.7)', dark: 'rgba(52, 211, 153, 0.7)', border: { light: 'rgb(5, 150, 105)', dark: 'rgb(16, 185, 129)' } },
            { light: 'rgba(245, 158, 11, 0.7)', dark: 'rgba(251, 191, 36, 0.7)', border: { light: 'rgb(217, 119, 6)', dark: 'rgb(245, 158, 11)' } },
            { light: 'rgba(239, 68, 68, 0.7)', dark: 'rgba(248, 113, 113, 0.7)', border: { light: 'rgb(220, 38, 38)', dark: 'rgb(239, 68, 68)' } },
            { light: 'rgba(139, 92, 246, 0.7)', dark: 'rgba(167, 139, 250, 0.7)', border: { light: 'rgb(124, 58, 237)', dark: 'rgb(139, 92, 246)' } },
            { light: 'rgba(14, 165, 233, 0.7)', dark: 'rgba(56, 189, 248, 0.7)', border: { light: 'rgb(3, 105, 161)', dark: 'rgb(14, 165, 233)' } },
        ];

        // Assign colors to data points
        for (let i = 0; i < labels.length; i++) {
            const colorIndex = i % baseColors.length;
            const isDark = document.documentElement.classList.contains('dark');

            colors.push(isDark ? baseColors[colorIndex].dark : baseColors[colorIndex].light);
            borderColors.push(isDark ? baseColors[colorIndex].border.dark : baseColors[colorIndex].border.light);
        }

        // Get theme information
        const isDark = document.documentElement.classList.contains('dark');

        // Chart configuration
        const chartConfig = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Employees',
                    data: data,
                    backgroundColor: colors,
                    borderColor: borderColors,
                    borderWidth: 1,
                    borderRadius: 4,
                    maxBarThickness: 40,
                    tension: 0.3 // For line chart smoothing
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: isDark ? '#1f2937' : '#ffffff',
                        titleColor: isDark ? '#f9fafb' : '#111827',
                        bodyColor: isDark ? '#d1d5db' : '#4b5563',
                        borderColor: isDark ? '#374151' : '#e5e7eb',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 6,
                        boxPadding: 4,
                        usePointStyle: true,
                        callbacks: {
                            label: function(context) {
                                return `Employees: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            color: isDark ? '#d1d5db' : '#4b5563',
                            font: { size: 11, family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' }
                        },
                        grid: {
                            color: isDark ? 'rgba(209, 213, 219, 0.1)' : 'rgba(107, 114, 128, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: isDark ? '#d1d5db' : '#4b5563',
                            font: { size: 11, family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 800,
                    easing: 'easeOutQuart'
                }
            }
        };

        // Create chart
        const employeeDistributionChart = new Chart(ctx, chartConfig);

        // Chart type toggle functionality
        const barChartBtn = document.getElementById('chartBarBtn');
        const lineChartBtn = document.getElementById('chartLineBtn');

        if (barChartBtn && lineChartBtn) {
            // Bar chart button
            barChartBtn.addEventListener('click', function() {
                if (employeeDistributionChart.config.type === 'bar') return;

                // Update UI
                barChartBtn.classList.add('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
                barChartBtn.classList.remove('text-gray-600', 'dark:text-gray-400');
                lineChartBtn.classList.remove('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
                lineChartBtn.classList.add('text-gray-600', 'dark:text-gray-400');

                // Update chart
                employeeDistributionChart.config.type = 'bar';
                employeeDistributionChart.update();
            });

            // Line chart button
            lineChartBtn.addEventListener('click', function() {
                if (employeeDistributionChart.config.type === 'line') return;

                // Update UI
                lineChartBtn.classList.add('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
                lineChartBtn.classList.remove('text-gray-600', 'dark:text-gray-400');
                barChartBtn.classList.remove('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
                barChartBtn.classList.add('text-gray-600', 'dark:text-gray-400');

                // Update chart
                employeeDistributionChart.config.type = 'line';
                employeeDistributionChart.update();
            });
        }

        // Update chart colors when theme changes
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const isDark = document.documentElement.classList.contains('dark');

                // Update dataset colors based on theme
                for (let i = 0; i < labels.length; i++) {
                    const colorIndex = i % baseColors.length;
                    employeeDistributionChart.data.datasets[0].backgroundColor[i] = isDark ? baseColors[colorIndex].dark : baseColors[colorIndex].light;
                    employeeDistributionChart.data.datasets[0].borderColor[i] = isDark ? baseColors[colorIndex].border.dark : baseColors[colorIndex].border.light;
                }

                // Update chart colors
                employeeDistributionChart.options.scales.x.ticks.color = isDark ? '#d1d5db' : '#4b5563';
                employeeDistributionChart.options.scales.y.ticks.color = isDark ? '#d1d5db' : '#4b5563';
                employeeDistributionChart.options.scales.y.grid.color = isDark ? 'rgba(209, 213, 219, 0.1)' : 'rgba(107, 114, 128, 0.1)';

                // Update tooltip colors
                employeeDistributionChart.options.plugins.tooltip.backgroundColor = isDark ? '#1f2937' : '#ffffff';
                employeeDistributionChart.options.plugins.tooltip.titleColor = isDark ? '#f9fafb' : '#111827';
                employeeDistributionChart.options.plugins.tooltip.bodyColor = isDark ? '#d1d5db' : '#4b5563';
                employeeDistributionChart.options.plugins.tooltip.borderColor = isDark ? '#374151' : '#e5e7eb';

                employeeDistributionChart.update();
            });
        }
    }
</script>
{% endblock %}
