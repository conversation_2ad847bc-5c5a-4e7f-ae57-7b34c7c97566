{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group %}
{% from "partials/forms/base_form.html" import form_group %}

{% block title %}Holiday Work Reports{% endblock %}

{% block header %}Holiday Work Reports{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title="Holiday Work Reports & Analytics",
    description="Comprehensive analytics and reporting for holiday work tracking and management."
  ) }}

  <!-- Date Range Filter -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Report Period</h3>
    </div>
    <div class="card-content">
      <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          {{ form_group(
            label="Start Date",
            name="start_date",
            type="date",
            value=start_date.strftime('%Y-%m-%d')
          ) }}

          {{ form_group(
            label="End Date",
            name="end_date",
            type="date",
            value=end_date.strftime('%Y-%m-%d')
          ) }}

          <div class="flex items-end">
            {% set filter_buttons = [
              {"text": "Update Report", "variant": "primary", "type": "submit", "icon": "refresh-cw"}
            ] %}
            {{ button_group(filter_buttons) }}
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Overview Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Holiday Work -->
    <div class="card">
      <div class="card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-muted-foreground">Total Holiday Work</p>
            <p class="text-2xl font-bold text-foreground">{{ overview_stats.total_holiday_work }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Approved Holiday Work -->
    <div class="card">
      <div class="card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-muted-foreground">Approved</p>
            <p class="text-2xl font-bold text-foreground">{{ overview_stats.approved_holiday_work }}</p>
            <p class="text-xs text-muted-foreground">{{ overview_stats.approval_rate }}% approval rate</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Holiday Work -->
    <div class="card">
      <div class="card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-muted-foreground">Pending</p>
            <p class="text-2xl font-bold text-foreground">{{ overview_stats.pending_holiday_work }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Unique Employees -->
    <div class="card">
      <div class="card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-muted-foreground">Employees</p>
            <p class="text-2xl font-bold text-foreground">{{ overview_stats.unique_employees }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Monthly Trends Chart -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Monthly Trends ({{ current_year }})</h3>
    </div>
    <div class="card-content">
      <div class="h-80">
        <canvas id="monthlyTrendsChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Employee Rankings and Holiday Breakdown -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Employee Rankings -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Top Employees (Holiday Work)</h3>
      </div>
      <div class="card-content">
        {% if employee_rankings.rankings %}
          <div class="space-y-3">
            {% for employee in employee_rankings.rankings %}
            <div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div class="flex items-center">
                <div class="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-primary">{{ employee.rank }}</span>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-foreground">{{ employee.employee_name }}</p>
                  <p class="text-xs text-muted-foreground">{{ employee.employee_number }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-foreground">{{ employee.holiday_work_count }} records</p>
                <p class="text-xs text-muted-foreground">{{ employee.approval_rate }}% approved</p>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-foreground">No holiday work records</h3>
            <p class="mt-1 text-sm text-muted-foreground">No employees have worked on holidays in this period.</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Holiday Breakdown -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Holiday Breakdown</h3>
      </div>
      <div class="card-content">
        {% if holiday_breakdown.holiday_breakdown %}
          <div class="space-y-3">
            {% for holiday in holiday_breakdown.holiday_breakdown[:10] %}
            <div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div>
                <p class="text-sm font-medium text-foreground">{{ holiday.holiday_name }}</p>
                <p class="text-xs text-muted-foreground">{{ holiday.date }} ({{ holiday.region_code }})</p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-foreground">{{ holiday.work_count }} employees</p>
                <p class="text-xs text-muted-foreground">{{ holiday.approval_rate }}% approved</p>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-1a4 4 0 014-4h4a4 4 0 014 4v1a4 4 0 11-8 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-foreground">No holiday work</h3>
            <p class="mt-1 text-sm text-muted-foreground">No work was performed on holidays in this period.</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Export Actions -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Export Reports</h3>
    </div>
    <div class="card-content">
      <div class="flex flex-wrap gap-2">
        {% set export_buttons = [
          {"text": "Export Overview", "variant": "outline", "onclick": "exportOverview()", "icon": "download"},
          {"text": "Export Employee Rankings", "variant": "outline", "onclick": "exportEmployeeRankings()", "icon": "download"},
          {"text": "Export Holiday Breakdown", "variant": "outline", "onclick": "exportHolidayBreakdown()", "icon": "download"}
        ] %}
        {{ button_group(export_buttons) }}
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Trends Chart
document.addEventListener('DOMContentLoaded', function() {
  const ctx = document.getElementById('monthlyTrendsChart').getContext('2d');
  
  const monthlyData = {{ monthly_trends.monthly_trends | tojson }};
  const labels = monthlyData.map(d => d.month_name);
  const totalData = monthlyData.map(d => d.total);
  const approvedData = monthlyData.map(d => d.approved);
  
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Total Holiday Work',
        data: totalData,
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.1
      }, {
        label: 'Approved Holiday Work',
        data: approvedData,
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Holiday Work Trends by Month'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        }
      }
    }
  });
});

// Export functions
function exportOverview() {
  const data = {{ overview_stats | tojson }};
  downloadJSON(data, 'holiday_work_overview.json');
}

function exportEmployeeRankings() {
  const data = {{ employee_rankings | tojson }};
  downloadJSON(data, 'holiday_work_employee_rankings.json');
}

function exportHolidayBreakdown() {
  const data = {{ holiday_breakdown | tojson }};
  downloadJSON(data, 'holiday_work_breakdown.json');
}

function downloadJSON(data, filename) {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
</script>
{% endblock %}
