<!DOCTYPE html>
{% set primary = app_settings.primary_color|default('#0284c7') %}
{% set neutral_colors = ['#71717a', '#64748b', '#78716c', '#6b7280', '#525252'] %}
<html lang="en" data-default-theme="{{ app_settings.default_theme|default('system') }}" class="theme-transition {% if primary.lower() in neutral_colors %}neutral-primary{% endif %} {% if active_page and active_page.startswith('admin_') %}admin-view{% endif %}">
<head>
  <!-- ================= -->
  <!-- Critical Resources -->
  <!-- ================= -->

  <!-- Error Handling & Initialization -->
  <script src="{{ url_for('static', filename='js/icons/icon-error-suppressor.js') }}"></script>
  <script src="{{ url_for('static', filename='js/core/critical-init.js') }}"></script>

  <!-- Meta Information -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="{{ app_settings.site_description|default('A powerful admin dashboard for managing your application.') }}">
  <title>{{ app_settings.site_name|default('Matrix') }} - {% block title %}{% endblock %}</title>

  <!-- Preload Resources -->
  <link rel="preload" href="{{ url_for('static', filename='js/icons/sprite.svg') }}" as="fetch" crossorigin="anonymous">

  <!-- Icon System -->
  <script src="{{ url_for('static', filename='js/icons/svg-sprite.js') }}"></script>

  <!-- Favicon & App Icons -->
  <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
  <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
  <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
  <link rel="icon" type="image/svg+xml" sizes="16x16" href="{{ url_for('static', filename='favicon.svg') }}">
  <meta name="apple-mobile-web-app-title" content="Matrix" />
  <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}" />

  <!-- ================= -->
  <!-- CSS Custom Variables -->
  <!-- ================= -->

  <!-- Theme & Color Variables -->
  <style>
    :root {
      /* Primary Color Variables */
      {% set rgb = hex_to_rgb(primary) %}
      --primary-hex: {{ primary }};
      --primary-rgb: {{ rgb[0] }}, {{ rgb[1] }}, {{ rgb[2] }};
      --primary: {{ hex_to_hsl(primary) }};

      /* Button Variables */
      {% if primary.lower() in neutral_colors %}
        /* For neutral colors (zinc, slate, stone, gray), default to light mode values */
        --button-bg: #000000;
        --button-text: #ffffff;
        --button-hover-bg: rgba(0, 0, 0, 0.8);
      {% else %}
        /* For other colors, use the actual color */
        --button-bg: {{ primary }};
        --button-text: {% if hex_to_rgb(primary)|sum < 382 %}#ffffff{% else %}#000000{% endif %};
        --button-hover-bg: {{ primary }};
        --button-hover-opacity: 0.9;
      {% endif %}

      /* Layout Variables */
      --submenu-indent: 1.5rem;
    }

    /* Dark Mode Overrides */
    html.dark:root {
      {% if primary.lower() in neutral_colors %}
        --button-bg: #ffffff;
        --button-text: #000000;
        --button-hover-bg: rgba(255, 255, 255, 0.9);
      {% endif %}
    }
  </style>
  <!-- =============== -->
  <!-- CSS Resources -->
  <!-- =============== -->

  <!-- Preload Critical CSS -->
  <link rel="preload" href="{{ url_for('static', filename='css/theme-colors.css') }}" as="style">
  <link rel="preload" href="{{ url_for('static', filename='css/main.css') }}" as="style">
  <link rel="preload" href="{{ url_for('static', filename='css/theme-transition.css') }}" as="style">

  <!-- Critical CSS (loaded first for optimal theme switching) -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-transition.css') }}">

  <!-- UI Component Styles (loaded asynchronously) -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/button-styles.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/modal.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/toast.css') }}" media="print" onload="this.media='all'">

  <!-- Animation Styles -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-animations.css') }}?v=6" media="print" onload="this.media='all'">

  <!-- Fallback for browsers that don't support onload -->
  <noscript>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/button-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modal.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/toast.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-animations.css') }}?v=6">
  </noscript>
</head>
<body class="bg-background text-foreground antialiased">
  {% if app_settings.maintenance_mode == 'on' or app_settings.maintenance_mode and not current_user.is_admin %}
  <!-- Maintenance Mode Banner -->
  <div class="bg-destructive text-white p-4 text-center">
    <div class="container mx-auto">
      <div class="flex items-center justify-center space-x-2">
        <i data-lucide="alert-triangle" class="w-5 h-5"></i>
        <p>The system is currently in maintenance mode. Some features may be unavailable.</p>
      </div>
    </div>
  </div>
  {% endif %}
  <div class="flex h-screen overflow-hidden">
    <!-- Include Sidebar Component -->
    {% include 'partials/sidebar.html' %}

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto bg-background p-0 {% if request.cookies.get('sidebarCollapsed') == 'true' %}lg:ml-0{% else %}lg:ml-64{% endif %} transition-all duration-300 ease-in-out" id="main-content" style="transition: margin 0.3s ease, opacity 0.3s ease;">
      <div class="flex items-center justify-between py-2 px-3 border-b border-border mb-0 dark:border-slate-800 bg-muted/30 dark:bg-slate-900/50">
        <div class="flex items-center">
          <div id="sidebar-toggle-container" class="relative w-12 h-12 flex items-center justify-center cursor-pointer">
            <i data-lucide="panel-left" class="h-5 w-5 z-10"></i>
            <button id="collapse-sidebar" class="absolute inset-0 w-full h-full rounded hover:bg-muted transition-colors z-20 opacity-80"></button>
          </div>
          <div class="h-5 mx-3 border-r border-border dark:border-slate-700"></div>
          <nav class="flex items-center text-sm">
            <a href="{{ url_for('admin.dashboard' if current_user.is_admin else 'main.user_dashboard') }}" class="text-muted-foreground hover:text-foreground transition-colors font-medium">{{ 'Admin' if current_user.is_admin else 'User' }}</a>
            <i data-lucide="chevron-right" class="h-4 w-4 mx-1 text-muted-foreground"></i>
            <span class="text-foreground font-medium">{% block header %}{% endblock %}</span>
          </nav>
        </div>
        <div class="flex items-center">
          <button id="theme-toggle" class="p-1 rounded hover:bg-muted transition-colors flex items-center justify-center" aria-label="Toggle theme" style="cursor: pointer;">
            <!-- Icon will be set by JavaScript for better theme transition -->
            <i data-lucide="sun" class="h-5 w-5 pointer-events-none"></i>
          </button>
        </div>
      </div>

      <!-- Flash Messages -->
      <div id="flash-messages" class="fixed bottom-4 right-4 z-50 max-w-md space-y-2">
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              {% set icon_class = "text-blue-500" %}
              {% set title = "Info" %}
              {% set icon = "info" %}

              {% if category == "error" %}
                {% set icon_class = "text-red-500" %}
                {% set title = "Error" %}
                {% set icon = "x-circle" %}
              {% elif category == "warning" %}
                {% set icon_class = "text-yellow-500" %}
                {% set title = "Warning" %}
                {% set icon = "alert-triangle" %}
              {% elif category == "success" %}
                {% set icon_class = "text-green-500" %}
                {% set title = "Success" %}
                {% set icon = "check" %}
              {% endif %}

              <div class="flash-message group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border border-border p-4 pr-8 shadow-lg transition-all bg-background text-foreground">
                <div class="grid gap-1">
                  <div class="flex items-center gap-2">
                    <div class="{{ icon_class }}">
                      <i data-lucide="{{ icon }}" class="h-4 w-4"></i>
                    </div>
                    <span class="text-sm font-semibold">{{ title }}</span>
                  </div>
                  <div class="text-sm opacity-90">{{ message }}</div>
                </div>
                <button class="absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 flash-close-btn">
                  <i data-lucide="x" class="h-4 w-4"></i>
                  <span class="sr-only">Close</span>
                </button>
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}
      </div>

      <div class="animate-fade-in px-5 py-4">
        {% block content %}{% endblock %}
      </div>
    </main>
  </div>

  <!-- Drawer Container for CRUD operations -->
  <div id="drawer-container"></div>

  <!-- Alert Dialog Container -->
  <div id="alert-dialog-container"></div>

  <!-- Modal Container -->
  <div id="modal-container"></div>

  <!-- JSON Viewer Modal -->
  {% include "components/json_viewer_modal.html" %}

  <!-- ===================== -->
  <!-- JavaScript Resources -->
  <!-- ===================== -->

  <!-- Core System Scripts -->
  <script src="{{ url_for('static', filename='js/core/asset-manager.js') }}"></script>
  <script src="{{ url_for('static', filename='js/core/utils.js') }}"></script>

  <!-- Icon System -->
  <script src="{{ url_for('static', filename='js/vendor/lucide/lucide.min.js') }}"></script>
  <script type="module" src="{{ url_for('static', filename='js/icons/icons-global.js') }}"></script>

  <!-- UI Components -->
  <script src="{{ url_for('static', filename='js/components/theme.js') }}"></script>
  <script src="{{ url_for('static', filename='js/components/sidebar.js') }}"></script>
  <script src="{{ url_for('static', filename='js/components/drawer.js') }}"></script>
  <script src="{{ url_for('static', filename='js/components/modal.js') }}"></script>
  <script src="{{ url_for('static', filename='js/components/form-utils.js') }}"></script>

  <!-- Conditional Form Handlers -->
  {% if active_page and (active_page in ['attendance_types', 'attendance_records', 'my_attendance', 'request_attendance', 'attendance_calendar', 'team_attendance']) %}
  <script src="{{ url_for('static', filename='js/forms/attendance-form.js') }}"></script>
  {% endif %}

  <!-- Feature Handlers -->
  <script src="{{ url_for('static', filename='js/flash-handler.js') }}"></script>

  <!-- Conditional Resources -->
  {% if active_page in ['admin_dashboard', 'admin_analytics', 'user_dashboard'] %}
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  {% endif %}

  <!-- Page-specific scripts -->
  {% block scripts %}{% endblock %}
</body>
</html>
