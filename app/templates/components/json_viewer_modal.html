<!-- Simple JSON Viewer Modal Template -->
<template id="json-viewer-modal-template">
  <div class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black/50" id="json-viewer-modal">
    <div class="relative w-full max-w-md max-h-[90vh] bg-background rounded-lg shadow-lg flex flex-col">
      <!-- Header -->
      <div class="flex items-start justify-between px-6 pt-6 pb-4">
        <div class="flex flex-col space-y-1 text-left pr-6">
          <h2 id="json-viewer-title" class="text-lg font-medium text-foreground">Entity Details</h2>
        </div>
        <button
          type="button"
          class="rounded-full opacity-70 text-muted-foreground hover:text-foreground h-8 w-8 inline-flex items-center justify-center transition-all duration-200 hover:opacity-100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 disabled:pointer-events-none"
          onclick="window.closeJsonModal()"
          aria-label="Close"
        >
          <i data-lucide="x" class="h-4 w-4 pointer-events-none"></i>
          <span class="sr-only">Close</span>
        </button>
      </div>

      <!-- Content -->
      <div id="json-viewer-content" class="px-6 pb-6 pt-2 overflow-y-auto flex-1">
        <div class="flex justify-center items-center py-16">
          <div class="animate-spin rounded-full h-8 w-8 border-2 border-t-transparent border-primary"></div>
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t border-border/10 px-6 py-4">
        <div class="flex flex-col-reverse sm:flex-row justify-between items-center gap-3">
          <div class="text-sm text-muted-foreground w-full sm:w-auto" id="json-viewer-info"></div>
          <div class="flex gap-3 w-full sm:w-auto justify-end">
            <button
              type="button"
              class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 disabled:pointer-events-none disabled:opacity-50 border border-border bg-background hover:bg-muted/50 h-10 px-4 py-2"
              onclick="window.closeJsonModal()"
            >
              <i data-lucide="x" class="h-4 w-4 mr-2"></i>
              <span>Close</span>
            </button>
            <button
              type="button"
              id="json-viewer-action"
              class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow h-10 px-4 py-2"
              onclick="window.copyJsonToClipboard()"
            >
              <i data-lucide="clipboard-copy" class="h-4 w-4 mr-2"></i>
              <span>Copy</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
