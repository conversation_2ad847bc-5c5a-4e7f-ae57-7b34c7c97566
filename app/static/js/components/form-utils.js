/**
 * Form Utilities Module
 *
 * This module provides utilities for form submission and handling.
 */

/**
 * Submit a form via AJAX
 * @param {string} formId - ID of the form to submit
 * @returns {Promise<Object>} - Promise resolving to the response data
 */
function submitDrawerForm(formId) {
  const form = document.getElementById(formId);
  if (!form) {
    console.error(`Form not found: ${formId}`);
    if (typeof showToast === 'function') {
      showToast('Form not found', { type: 'error' });
    }
    return Promise.reject(new Error('Form not found'));
  }

  // Check if the form is valid
  if (!validateDrawerForm(form)) {
    // Form is invalid, don't submit
    if (typeof showToast === 'function') {
      showToast('Please fix the errors in the form', { type: 'error' });
    }
    // Return an empty resolved promise to avoid console errors
    return Promise.resolve();
  }

  // Get form data
  const formData = new FormData(form);

  // Get form action
  const action = form.getAttribute('action');
  if (!action) {
    console.error('Form action not specified');
    if (typeof showToast === 'function') {
      showToast('Form action not specified', { type: 'error' });
    }
    return Promise.resolve(); // Return resolved promise to avoid console errors
  }

  // Get form method
  const method = form.getAttribute('method') || 'POST';

  // Submit the form
  return fetch(action, {
    method: method,
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest' // Mark as AJAX request
    }
  })
    .then(response => {
      // First check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        // Try to parse the error response as JSON to get validation errors
        return response.json().then(errorData => {
          throw new Error(errorData.message || `HTTP error! Status: ${response.status}`, { cause: errorData });
        }).catch(jsonError => {
          // If we can't parse as JSON, just throw the HTTP error
          if (jsonError.message && jsonError.cause) {
            throw jsonError; // Re-throw our custom error with the parsed data
          }
          throw new Error(`HTTP error! Status: ${response.status}`);
        });
      }
      return response.json();
    })
    .then(data => {
      // Show success message
      if (typeof showToast === 'function') {
        showToast(data.message || 'Form submitted successfully', { type: 'success' });
      }

      // Find drawer container
      const drawerContainer = form.closest('.drawer');
      if (drawerContainer && window.drawerManager) {
        // Close the drawer
        window.drawerManager.closeLast();
      }

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 500);

      return data;
    })
    .catch(error => {
      // Check if this is a client-side validation error
      if (error.cause && error.cause.clientValidation) {
        // No need to show toast again as it was already shown during validation
        // Silent return without rejecting to avoid console errors
        return;
      }
      // Check if the error has validation errors attached
      else if (error.cause && error.cause.errors) {
        // Display validation errors on the form
        displayValidationErrors(form, error.cause.errors);

        if (typeof showToast === 'function') {
          showToast(error.message || 'Please fix the errors in the form', { type: 'error' });
        }
        // Silent return without rejecting to avoid console errors
        return;
      } else {
        // Only log actual errors to console
        console.error('Form submission error:', error);

        // Generic error
        if (typeof showToast === 'function') {
          showToast(`Form submission failed: ${error.message}`, { type: 'error' });
        }
      }

      // Only reject for actual errors, not for validation
      return Promise.reject(error);
    });
}

/**
 * Validate a form
 * @param {HTMLFormElement} form - Form to validate
 * @returns {boolean} - Whether the form is valid
 */
function validateDrawerForm(form) {
  // Get all required fields that are visible
  const requiredFields = form.querySelectorAll('[required]');
  let isValid = true;

  // Check each required field
  requiredFields.forEach(field => {
    // Skip validation for hidden fields or fields in hidden containers
    const isFieldVisible = field.offsetParent !== null &&
                          !field.closest('[style*="display: none"]') &&
                          !field.closest('[style*="display:none"]');

    if (!isFieldVisible) {
      // Remove any existing errors for hidden fields
      field.classList.remove('border-destructive');
      const errorElement = field.nextElementSibling;
      if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.remove();
      }
      return; // Skip validation for hidden fields
    }

    if (!field.value.trim()) {
      isValid = false;
      field.classList.add('border-destructive');

      // Get error message
      const errorMessage = field.getAttribute('data-error-message') || 'This field is required';

      // Find or create error element
      let errorElement = field.nextElementSibling;
      if (!errorElement || !errorElement.classList.contains('error-message')) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message text-xs text-destructive mt-1';
        field.insertAdjacentElement('afterend', errorElement);
      }

      errorElement.textContent = errorMessage;
    } else {
      // Remove error class
      field.classList.remove('border-destructive');

      // Remove error message
      const errorElement = field.nextElementSibling;
      if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.remove();
      }
    }
  });

  return isValid;
}

// Add to window object so it can be called from HTML
window.submitDrawerForm = submitDrawerForm;
window.validateDrawerForm = validateDrawerForm;

/**
 * Display validation errors on the form
 * @param {HTMLFormElement} form - Form to display errors on
 * @param {Object} errors - Object containing field names and error messages
 */
function displayValidationErrors(form, errors) {
  // Clear any existing errors first
  form.querySelectorAll('.error-message').forEach(el => el.remove());
  form.querySelectorAll('.border-destructive').forEach(el => el.classList.remove('border-destructive'));

  // Add new errors
  for (const [fieldName, fieldErrors] of Object.entries(errors)) {
    const field = form.querySelector(`[name="${fieldName}"]`);
    if (!field) continue;

    // Add error class to the field
    field.classList.add('border-destructive');

    // Create error message element
    const errorElement = document.createElement('ul');
    errorElement.className = 'error-message text-xs text-destructive mt-1';

    // Add each error message
    fieldErrors.forEach(error => {
      const li = document.createElement('li');
      li.textContent = error;
      errorElement.appendChild(li);
    });

    // Insert after the field
    field.parentNode.insertBefore(errorElement, field.nextSibling);

    // Focus the first field with an error
    if (field === form.querySelector('.border-destructive')) {
      field.focus();
    }
  }

  // If there's a general CSRF error (often a key in errors but not a field name)
  if (errors.csrf_token) {
    if (typeof showToast === 'function') {
      showToast('Security token expired. Please try again.', { type: 'error' });
    }
  }
}
