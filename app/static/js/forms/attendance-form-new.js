/**
 * Attendance Form Module
 *
 * This module handles the dynamic behavior for attendance record forms,
 * including holiday work field visibility and form validation.
 */

console.log('DEBUG: Attendance form script loaded');

/**
 * Core holiday work functionality that can be called from anywhere
 */
function setupHolidayWorkFunctionality() {
  const holidayCheckbox = document.getElementById('is_holiday_work');
  const holidayReasonField = document.getElementById('holidayWorkReasonField');
  const reasonTextarea = document.getElementById('holiday_work_reason');

  console.log('DEBUG: Elements found:', {
    checkbox: !!holidayCheckbox,
    reasonField: !!holidayReasonField,
    textarea: !!reasonTextarea
  });

  if (!holidayCheckbox || !holidayReasonField) {
    console.warn('DEBUG: Holiday work elements not found');
    return false;
  }

  // Function to toggle visibility
  function toggleHolidayReason() {
    console.log('DEBUG: Toggling holiday reason field, checked:', holidayCheckbox.checked);

    if (holidayCheckbox.checked) {
      holidayReasonField.style.display = 'block';
      if (reasonTextarea) {
        reasonTextarea.setAttribute('required', 'required');
      }
    } else {
      holidayReasonField.style.display = 'none';
      if (reasonTextarea) {
        reasonTextarea.removeAttribute('required');
        reasonTextarea.value = '';
      }
    }
  }

  // Remove existing event listeners to prevent duplicates
  const newCheckbox = holidayCheckbox.cloneNode(true);
  holidayCheckbox.parentNode.replaceChild(newCheckbox, holidayCheckbox);

  // Add event listener
  newCheckbox.addEventListener('change', toggleHolidayReason);

  // Set initial state
  toggleHolidayReason();

  console.log('DEBUG: Holiday work functionality set up successfully');
  return true;
}

/**
 * Watch for attendance form elements and initialize when found
 */
function watchForAttendanceForm() {
  console.log('DEBUG: Starting form watcher...');

  // Check if elements already exist
  if (setupHolidayWorkFunctionality()) {
    console.log('DEBUG: Elements found immediately, setup complete');
    return;
  }

  // Use MutationObserver to watch for dynamically added content
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      mutation.addedNodes.forEach(function(node) {
        if (node.nodeType === 1) { // Element node
          // Check if this node or its descendants contain our target elements
          if (node.id === 'is_holiday_work' ||
              (node.querySelector && node.querySelector('#is_holiday_work'))) {
            console.log('DEBUG: Holiday work elements detected via mutation observer');
            if (setupHolidayWorkFunctionality()) {
              observer.disconnect(); // Stop watching once successful
            }
          }
        }
      });
    });
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Fallback: try again after delays
  setTimeout(function() {
    console.log('DEBUG: Fallback attempt after 300ms');
    if (setupHolidayWorkFunctionality()) {
      observer.disconnect();
    }
  }, 300);

  setTimeout(function() {
    console.log('DEBUG: Final fallback attempt after 1000ms');
    if (setupHolidayWorkFunctionality()) {
      observer.disconnect();
    }
  }, 1000);
}

/**
 * Global initialization function for attendance forms
 * Called by the drawer system when forms are loaded dynamically
 */
window.initializeAttendanceForm = function() {
  console.log('DEBUG: initializeAttendanceForm called by drawer system');

  // Small delay to ensure DOM is fully rendered
  setTimeout(function() {
    setupHolidayWorkFunctionality();
  }, 50);
};

// Global test function for debugging
window.testHolidayWork = function() {
  console.log('DEBUG: Manual test function called');
  const result = setupHolidayWorkFunctionality();
  console.log('DEBUG: Manual test result:', result);
  return result;
};

// Global function to force watch restart
window.restartFormWatch = function() {
  console.log('DEBUG: Restarting form watch');
  watchForAttendanceForm();
};

// Start watching immediately when script loads
console.log('DEBUG: Starting immediate form watch');
watchForAttendanceForm();

// Also try to initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('DEBUG: DOMContentLoaded event fired');
  watchForAttendanceForm();
});

// And on window load as final fallback
window.addEventListener('load', function() {
  console.log('DEBUG: Window load event fired');
  watchForAttendanceForm();
});
