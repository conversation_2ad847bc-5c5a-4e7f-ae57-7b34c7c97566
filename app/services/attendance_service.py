"""
Attendance service layer for business logic related to attendance records.
"""
from datetime import date, timedelta, datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_

from app import db
from app.models.attendance import AttendanceRecord, AttendanceType, Holiday
from app.models.employee import EmployeeDetail
from app.services.holiday_service import HolidayService


class AttendanceService:
    """Service class for attendance-related business logic."""

    @staticmethod
    def create_attendance_record(employee_detail_id: int, attendance_type_id: int,
                               record_date: date, **kwargs) -> Dict[str, Any]:
        """
        Create a new attendance record with validation.

        Args:
            employee_detail_id: ID of the employee
            attendance_type_id: ID of the attendance type
            record_date: Date of the attendance record
            **kwargs: Additional fields (start_time, end_time, duration_hours, notes, etc.)

        Returns:
            Dictionary with creation result
        """
        try:
            # Validate employee exists
            employee = EmployeeDetail.query.get(employee_detail_id)
            if not employee:
                return {
                    'success': False,
                    'error': 'Employee not found'
                }

            # Validate attendance type exists
            attendance_type = AttendanceType.query.get(attendance_type_id)
            if not attendance_type:
                return {
                    'success': False,
                    'error': 'Attendance type not found'
                }

            # Check for duplicate records
            duplicate_check = AttendanceService.check_duplicate_record(
                employee_detail_id, record_date
            )
            if duplicate_check['has_duplicate']:
                return {
                    'success': False,
                    'error': 'An attendance record already exists for this employee on this date'
                }

            # Check holiday conflicts and get holiday information
            holiday_info = AttendanceService.check_holiday_date(employee_detail_id, record_date)

            # Determine if this is holiday work - respect manual override from form
            manual_holiday_work = kwargs.get('is_holiday_work', False)
            auto_detected_holiday = holiday_info.get('is_holiday', False)

            # Use manual setting if provided, otherwise use auto-detection
            is_holiday_work = manual_holiday_work if manual_holiday_work is not None else auto_detected_holiday
            holiday_work_reason = kwargs.get('holiday_work_reason', '')

            # Create the record
            record = AttendanceRecord(
                employee_detail_id=employee_detail_id,
                attendance_type_id=attendance_type_id,
                date=record_date,
                start_time=kwargs.get('start_time'),
                end_time=kwargs.get('end_time'),
                duration_hours=kwargs.get('duration_hours'),
                notes=kwargs.get('notes'),
                status=kwargs.get('status', AttendanceRecord.STATUS_PENDING),
                is_holiday_work=is_holiday_work,
                holiday_work_reason=holiday_work_reason if is_holiday_work else None
            )

            # Enhanced business rules for status determination
            final_status = kwargs.get('status', AttendanceRecord.STATUS_PENDING)

            # Smart status selection logic
            if final_status == AttendanceRecord.STATUS_PENDING:
                # Apply auto-approval logic only if status wasn't explicitly set
                if is_holiday_work:
                    # Holiday work may require special approval workflow
                    if attendance_type.requires_approval:
                        # Keep as pending for manager approval
                        record.status = AttendanceRecord.STATUS_PENDING
                    else:
                        # Auto-approve but flag for tracking
                        record.status = AttendanceRecord.STATUS_AUTO_APPROVED
                        record.approved_at = datetime.now()
                else:
                    # Normal attendance logic
                    if not attendance_type.requires_approval:
                        record.status = AttendanceRecord.STATUS_AUTO_APPROVED
                        record.approved_at = datetime.now()
                    else:
                        record.status = AttendanceRecord.STATUS_PENDING
            else:
                # Use explicitly provided status
                record.status = final_status
                if final_status in [AttendanceRecord.STATUS_APPROVED, AttendanceRecord.STATUS_AUTO_APPROVED]:
                    record.approved_at = datetime.now()

            db.session.add(record)
            db.session.commit()

            # Prepare response message
            message = 'Attendance record created successfully'
            if is_holiday_work:
                holiday_name = holiday_info.get('holiday_name', 'Holiday')
                message += f' (flagged as holiday work for {holiday_name})'

            return {
                'success': True,
                'record': record,
                'holiday_info': holiday_info,
                'is_holiday_work': is_holiday_work,
                'message': message
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'Error creating attendance record: {str(e)}'
            }

    @staticmethod
    def update_attendance_record(record_id: int, **kwargs) -> Dict[str, Any]:
        """
        Update an existing attendance record with holiday work detection.

        Args:
            record_id: ID of the record to update
            **kwargs: Fields to update

        Returns:
            Dictionary with update result
        """
        try:
            record = AttendanceRecord.query.get(record_id)
            if not record:
                return {
                    'success': False,
                    'error': 'Attendance record not found'
                }

            # Check if date is being updated
            new_date = kwargs.get('date')
            date_changed = new_date and new_date != record.date

            # Update fields
            for field, value in kwargs.items():
                if hasattr(record, field):
                    setattr(record, field, value)

            # Handle holiday work detection and manual override
            holiday_info = {}
            if date_changed or 'date' in kwargs:
                check_date = new_date if new_date else record.date
                holiday_info = AttendanceService.check_holiday_date(record.employee_detail_id, check_date)

            # Respect manual holiday work setting if provided, otherwise use auto-detection
            if 'is_holiday_work' in kwargs:
                # Manual setting provided - use it
                record.is_holiday_work = kwargs['is_holiday_work']
            elif date_changed:
                # Date changed but no manual setting - use auto-detection
                record.is_holiday_work = holiday_info.get('is_holiday', False)
            # If neither date changed nor manual setting provided, keep existing value

            # Handle holiday work reason
            if 'holiday_work_reason' in kwargs:
                record.holiday_work_reason = kwargs['holiday_work_reason']
            elif not record.is_holiday_work:
                # Clear reason if not holiday work
                record.holiday_work_reason = None

            db.session.commit()

            # Prepare response message
            message = 'Attendance record updated successfully'
            if holiday_info.get('is_holiday'):
                holiday_name = holiday_info.get('holiday_name', 'Holiday')
                message += f' (flagged as holiday work for {holiday_name})'

            return {
                'success': True,
                'record': record,
                'holiday_info': holiday_info,
                'message': message
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'Error updating attendance record: {str(e)}'
            }

    @staticmethod
    def delete_attendance_record(record_id: int) -> Dict[str, Any]:
        """
        Delete an attendance record.

        Args:
            record_id: ID of the record to delete

        Returns:
            Dictionary with deletion result
        """
        try:
            record = AttendanceRecord.query.get(record_id)
            if not record:
                return {
                    'success': False,
                    'error': 'Attendance record not found'
                }

            db.session.delete(record)
            db.session.commit()

            return {
                'success': True,
                'message': 'Attendance record deleted successfully'
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'Error deleting attendance record: {str(e)}'
            }

    @staticmethod
    def check_duplicate_record(employee_detail_id: int, record_date: date,
                             exclude_record_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Check if a duplicate attendance record exists.

        Args:
            employee_detail_id: ID of the employee
            record_date: Date to check
            exclude_record_id: Optional record ID to exclude from check (for updates)

        Returns:
            Dictionary with duplicate check result
        """
        query = AttendanceRecord.query.filter(
            AttendanceRecord.employee_detail_id == employee_detail_id,
            AttendanceRecord.date == record_date
        )

        if exclude_record_id:
            query = query.filter(AttendanceRecord.id != exclude_record_id)

        existing_record = query.first()

        return {
            'has_duplicate': existing_record is not None,
            'existing_record': existing_record
        }

    @staticmethod
    def check_holiday_date(employee_detail_id: int, record_date: date) -> Dict[str, Any]:
        """
        Check if the attendance date is a holiday and provide relevant information.

        Args:
            employee_detail_id: ID of the employee
            record_date: Date to check

        Returns:
            Dictionary with holiday information
        """
        try:
            # Get employee region (for now, default to PH - will be enhanced in Task 1.3)
            region_code = AttendanceService.get_employee_region(employee_detail_id)

            # Check if date is a holiday
            is_holiday = HolidayService.is_holiday(record_date, region_code)

            if not is_holiday:
                return {
                    'is_holiday': False,
                    'region_code': region_code
                }

            # Get holiday details
            holiday = Holiday.query.filter(
                Holiday.date == record_date,
                or_(Holiday.region_code == region_code, Holiday.region_code == 'GLOBAL')
            ).first()

            return {
                'is_holiday': True,
                'holiday_name': holiday.name if holiday else 'Unknown Holiday',
                'holiday_description': holiday.description if holiday else '',
                'region_code': holiday.region_code if holiday else region_code,
                'holiday_id': holiday.id if holiday else None
            }

        except Exception as e:
            return {
                'is_holiday': False,
                'error': str(e)
            }

    @staticmethod
    def get_employee_region(employee_detail_id: int) -> str:
        """
        Get the region code for an employee using enhanced detection.

        Args:
            employee_detail_id: ID of the employee

        Returns:
            Region code (e.g., 'US', 'PH', 'GLOBAL')
        """
        # Use the enhanced region detection from HolidayService
        return HolidayService.get_employee_region(employee_detail_id)

    @staticmethod
    def get_attendance_records(employee_id: Optional[int] = None,
                             start_date: Optional[date] = None,
                             end_date: Optional[date] = None,
                             status: Optional[str] = None,
                             attendance_type_id: Optional[int] = None) -> List[AttendanceRecord]:
        """
        Get attendance records with optional filters.

        Args:
            employee_id: Optional employee ID filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            status: Optional status filter
            attendance_type_id: Optional attendance type filter

        Returns:
            List of AttendanceRecord objects
        """
        query = AttendanceRecord.query

        if employee_id:
            query = query.filter(AttendanceRecord.employee_detail_id == employee_id)

        if start_date:
            query = query.filter(AttendanceRecord.date >= start_date)

        if end_date:
            query = query.filter(AttendanceRecord.date <= end_date)

        if status:
            query = query.filter(AttendanceRecord.status == status)

        if attendance_type_id:
            query = query.filter(AttendanceRecord.attendance_type_id == attendance_type_id)

        return query.order_by(AttendanceRecord.date.desc()).all()

    @staticmethod
    def approve_attendance_record(record_id: int, approved_by_id: int,
                                notes: Optional[str] = None) -> Dict[str, Any]:
        """
        Approve an attendance record.

        Args:
            record_id: ID of the record to approve
            approved_by_id: ID of the user approving the record
            notes: Optional approval notes

        Returns:
            Dictionary with approval result
        """
        try:
            record = AttendanceRecord.query.get(record_id)
            if not record:
                return {
                    'success': False,
                    'error': 'Attendance record not found'
                }

            record.status = AttendanceRecord.STATUS_APPROVED
            record.approved_by_id = approved_by_id
            record.approved_at = datetime.now()

            if notes:
                record.notes = f"{record.notes}\n\nApproval notes: {notes}" if record.notes else f"Approval notes: {notes}"

            db.session.commit()

            return {
                'success': True,
                'record': record,
                'message': 'Attendance record approved successfully'
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'Error approving attendance record: {str(e)}'
            }

    @staticmethod
    def reject_attendance_record(record_id: int, rejected_by_id: int,
                               rejection_reason: str) -> Dict[str, Any]:
        """
        Reject an attendance record.

        Args:
            record_id: ID of the record to reject
            rejected_by_id: ID of the user rejecting the record
            rejection_reason: Reason for rejection

        Returns:
            Dictionary with rejection result
        """
        try:
            record = AttendanceRecord.query.get(record_id)
            if not record:
                return {
                    'success': False,
                    'error': 'Attendance record not found'
                }

            record.status = AttendanceRecord.STATUS_REJECTED
            record.approved_by_id = rejected_by_id
            record.approved_at = datetime.now()
            record.rejection_reason = rejection_reason

            db.session.commit()

            return {
                'success': True,
                'record': record,
                'message': 'Attendance record rejected successfully'
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'Error rejecting attendance record: {str(e)}'
            }

    @staticmethod
    def get_holiday_work_records(employee_id: Optional[int] = None,
                               start_date: Optional[date] = None,
                               end_date: Optional[date] = None,
                               status: Optional[str] = None) -> List[AttendanceRecord]:
        """
        Get holiday work records with optional filters.

        Args:
            employee_id: Optional employee ID filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            status: Optional status filter

        Returns:
            List of AttendanceRecord objects that are holiday work
        """
        return AttendanceRecord.get_holiday_work_records(start_date, end_date, employee_id, status)

    @staticmethod
    def get_holiday_work_statistics(start_date: Optional[date] = None,
                                  end_date: Optional[date] = None) -> Dict[str, Any]:
        """
        Get holiday work statistics.

        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            Dictionary with holiday work statistics
        """
        return AttendanceRecord.get_holiday_work_stats(start_date, end_date)

    @staticmethod
    def flag_existing_holiday_work(start_date: Optional[date] = None,
                                 end_date: Optional[date] = None) -> Dict[str, Any]:
        """
        Scan existing attendance records and flag holiday work that wasn't previously detected.

        Args:
            start_date: Optional start date for scanning
            end_date: Optional end date for scanning

        Returns:
            Dictionary with scan results
        """
        try:
            # Get all attendance records in date range that aren't already flagged as holiday work
            query = AttendanceRecord.query.filter(AttendanceRecord.is_holiday_work == False)

            if start_date:
                query = query.filter(AttendanceRecord.date >= start_date)
            if end_date:
                query = query.filter(AttendanceRecord.date <= end_date)

            records = query.all()

            flagged_count = 0
            errors = []

            for record in records:
                try:
                    # Check if this date is a holiday
                    holiday_info = AttendanceService.check_holiday_date(record.employee_detail_id, record.date)

                    if holiday_info.get('is_holiday', False):
                        # Flag as holiday work
                        record.is_holiday_work = True
                        record.holiday_work_reason = f"Auto-flagged: {holiday_info.get('holiday_name', 'Holiday')}"
                        flagged_count += 1

                except Exception as e:
                    errors.append(f"Error processing record {record.id}: {str(e)}")

            if flagged_count > 0:
                db.session.commit()

            return {
                'success': True,
                'flagged_count': flagged_count,
                'total_scanned': len(records),
                'errors': errors,
                'message': f'Successfully flagged {flagged_count} holiday work records out of {len(records)} scanned'
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'Error scanning for holiday work: {str(e)}'
            }

    @staticmethod
    def validate_holiday_work_data(employee_detail_id: int, record_date: date,
                                 holiday_work_reason: str = '') -> Dict[str, Any]:
        """
        Validate holiday work data and provide recommendations.

        Args:
            employee_detail_id: ID of the employee
            record_date: Date of the attendance record
            holiday_work_reason: Reason for working on holiday

        Returns:
            Dictionary with validation results
        """
        try:
            # Check if date is actually a holiday
            holiday_info = AttendanceService.check_holiday_date(employee_detail_id, record_date)

            errors = []
            warnings = []
            recommendations = []

            if not holiday_info.get('is_holiday', False):
                errors.append("The selected date is not a holiday")
            else:
                holiday_name = holiday_info.get('holiday_name', 'Holiday')

                # Check if reason is provided
                if not holiday_work_reason or len(holiday_work_reason.strip()) < 10:
                    warnings.append("Holiday work reason should be at least 10 characters long")
                    recommendations.append(f"Please provide a detailed reason for working on {holiday_name}")

                # Check for existing holiday work on same date
                existing_records = AttendanceRecord.query.filter(
                    AttendanceRecord.employee_detail_id == employee_detail_id,
                    AttendanceRecord.date == record_date,
                    AttendanceRecord.is_holiday_work == True
                ).count()

                if existing_records > 0:
                    warnings.append(f"Employee already has {existing_records} holiday work record(s) on this date")

            return {
                'is_valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'recommendations': recommendations,
                'holiday_info': holiday_info
            }

        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f'Validation error: {str(e)}'],
                'warnings': [],
                'recommendations': []
            }
