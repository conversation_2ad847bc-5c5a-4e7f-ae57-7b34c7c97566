"""
API routes for attendance management.
Provides REST endpoints for attendance records and calendar data.
"""

from flask import request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, date
from sqlalchemy import and_, extract
from sqlalchemy.orm import joinedload

from app.models.attendance import AttendanceRecord
from app.services.attendance_service import AttendanceService
from app.routes.api.utils import handle_api_error

# Import the API Blueprint from the api package
from app.routes.api import api_bp


@api_bp.route('/attendance/calendar', methods=['GET'])
@login_required
@handle_api_error
def get_user_attendance_calendar():
    """Get user's attendance calendar data for a specific month/year."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return jsonify({
            'success': False,
            'error': 'User does not have employee details.'
        }), 400

    # Get query parameters
    year = request.args.get('year', date.today().year, type=int)
    month = request.args.get('month', date.today().month, type=int)

    # Validate year and month
    current_year = date.today().year
    if year < current_year - 10 or year > current_year + 10:
        return jsonify({
            'success': False,
            'error': f'Year must be within 10 years of current year ({current_year - 10} to {current_year + 10}).'
        }), 400

    if month < 1 or month > 12:
        return jsonify({
            'success': False,
            'error': 'Month must be between 1 and 12.'
        }), 400

    try:
        # Debug logging for May 2025 issue
        if year == 2025 and month == 5:
            current_app.logger.info(f"Debugging May 2025 request - User: {current_user.id}, Employee: {current_user.employee_detail.id if current_user.employee_detail else 'None'}")

        # Get user's attendance records for the specified month
        attendance_records = AttendanceRecord.query.filter(
            and_(
                AttendanceRecord.employee_detail_id == current_user.employee_detail.id,
                extract('year', AttendanceRecord.date) == year,
                extract('month', AttendanceRecord.date) == month
            )
        ).options(
            joinedload(AttendanceRecord.attendance_type)
        ).all()

        # Debug logging for May 2025 issue
        if year == 2025 and month == 5:
            current_app.logger.info(f"Found {len(attendance_records)} records for May 2025")

        # Convert to calendar format
        calendar_data = {}
        for record in attendance_records:
            date_str = record.date.strftime('%Y-%m-%d')
            calendar_data[date_str] = {
                'id': record.id,
                'type': record.attendance_type.name,
                'type_code': record.attendance_type.code,
                'status': record.status,
                'status_display': record.get_status_display(),
                'start_time': record.start_time.strftime('%H:%M') if record.start_time else None,
                'end_time': record.end_time.strftime('%H:%M') if record.end_time else None,
                'duration_hours': float(record.duration_hours) if record.duration_hours else None,
                'notes': record.notes,
                'is_full_day': record.attendance_type.is_full_day,
                'color': record.attendance_type.color_code,
                'is_holiday_work': record.is_holiday_work,
                'holiday_work_reason': record.holiday_work_reason,
                'created_at': record.created_at.isoformat(),
                'approved_at': record.approved_at.isoformat() if record.approved_at else None
            }

        return jsonify({
            'success': True,
            'year': year,
            'month': month,
            'employee_id': current_user.employee_detail.id,
            'calendar_data': calendar_data,
            'total_records': len(attendance_records)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to fetch attendance calendar data: {str(e)}'
        }), 500


@api_bp.route('/attendance/summary', methods=['GET'])
@login_required
@handle_api_error
def get_user_attendance_summary():
    """Get user's attendance summary for a specific period."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return jsonify({
            'success': False,
            'error': 'User does not have employee details.'
        }), 400

    # Get query parameters
    year = request.args.get('year', date.today().year, type=int)
    month = request.args.get('month', type=int)

    try:
        # Build base query
        query = AttendanceRecord.query.filter(
            AttendanceRecord.employee_detail_id == current_user.employee_detail.id,
            extract('year', AttendanceRecord.date) == year
        )

        # Add month filter if specified
        if month:
            query = query.filter(extract('month', AttendanceRecord.date) == month)

        # Get records with attendance type information
        records = query.options(joinedload(AttendanceRecord.attendance_type)).all()

        # Calculate summary statistics
        summary = {
            'total_records': len(records),
            'by_status': {},
            'by_type': {},
            'by_month': {} if not month else None
        }

        # Group by status
        for record in records:
            status = record.status
            if status not in summary['by_status']:
                summary['by_status'][status] = 0
            summary['by_status'][status] += 1

        # Group by type
        for record in records:
            type_name = record.attendance_type.name
            if type_name not in summary['by_type']:
                summary['by_type'][type_name] = 0
            summary['by_type'][type_name] += 1

        # Group by month (if year view)
        if not month:
            for record in records:
                month_key = record.date.strftime('%Y-%m')
                if month_key not in summary['by_month']:
                    summary['by_month'][month_key] = 0
                summary['by_month'][month_key] += 1

        return jsonify({
            'success': True,
            'year': year,
            'month': month,
            'employee_id': current_user.employee_detail.id,
            'summary': summary
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to fetch attendance summary: {str(e)}'
        }), 500


@api_bp.route('/attendance/check-conflicts', methods=['GET'])
@login_required
@handle_api_error
def check_attendance_conflicts():
    """Check for attendance conflicts on a specific date."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return jsonify({
            'success': False,
            'error': 'User does not have employee details.'
        }), 400

    date_str = request.args.get('date')
    if not date_str:
        return jsonify({
            'success': False,
            'error': 'Date parameter is required.'
        }), 400

    try:
        check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({
            'success': False,
            'error': 'Invalid date format. Use YYYY-MM-DD.'
        }), 400

    try:
        # Check for existing attendance records
        existing_record = AttendanceRecord.query.filter(
            and_(
                AttendanceRecord.employee_detail_id == current_user.employee_detail.id,
                AttendanceRecord.date == check_date
            )
        ).first()

        # Check for holidays
        holiday_info = AttendanceService.check_holiday_date(
            current_user.employee_detail.id,
            check_date
        )

        return jsonify({
            'success': True,
            'date': date_str,
            'has_existing_record': existing_record is not None,
            'existing_record': {
                'id': existing_record.id,
                'type': existing_record.attendance_type.name,
                'status': existing_record.status
            } if existing_record else None,
            'holiday_info': holiday_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to check attendance conflicts: {str(e)}'
        }), 500


@api_bp.route('/attendance/holiday-work', methods=['GET'])
@login_required
@handle_api_error
def get_holiday_work_records():
    """Get holiday work records with optional filtering."""
    # Get query parameters
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    status = request.args.get('status')

    # Parse dates
    start_date = None
    end_date = None

    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid start_date format. Use YYYY-MM-DD.'
            }), 400

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid end_date format. Use YYYY-MM-DD.'
            }), 400

    try:
        # Get holiday work records
        employee_id = current_user.employee_detail.id if current_user.employee_detail else None
        if not employee_id:
            return jsonify({
                'success': False,
                'error': 'User does not have employee details.'
            }), 400

        records = AttendanceService.get_holiday_work_records(
            employee_id=employee_id,
            start_date=start_date,
            end_date=end_date,
            status=status
        )

        # Convert to JSON format
        records_data = []
        for record in records:
            records_data.append(record.to_dict())

        return jsonify({
            'success': True,
            'records': records_data,
            'total_count': len(records_data),
            'filters': {
                'start_date': start_date_str,
                'end_date': end_date_str,
                'status': status,
                'employee_id': employee_id
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to fetch holiday work records: {str(e)}'
        }), 500


@api_bp.route('/attendance/holiday-work/statistics', methods=['GET'])
@login_required
@handle_api_error
def get_holiday_work_statistics():
    """Get holiday work statistics."""
    # Get query parameters
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    # Parse dates
    start_date = None
    end_date = None

    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid start_date format. Use YYYY-MM-DD.'
            }), 400

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid end_date format. Use YYYY-MM-DD.'
            }), 400

    try:
        # Get holiday work statistics
        stats = AttendanceService.get_holiday_work_statistics(start_date, end_date)

        return jsonify({
            'success': True,
            'statistics': stats,
            'period': {
                'start_date': start_date_str,
                'end_date': end_date_str
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to fetch holiday work statistics: {str(e)}'
        }), 500


@api_bp.route('/attendance/holiday-work/validate', methods=['POST'])
@login_required
@handle_api_error
def validate_holiday_work():
    """Validate holiday work data."""
    # Ensure user has employee details
    if not current_user.employee_detail:
        return jsonify({
            'success': False,
            'error': 'User does not have employee details.'
        }), 400

    data = request.get_json()
    if not data:
        return jsonify({
            'success': False,
            'error': 'JSON data is required.'
        }), 400

    date_str = data.get('date')
    holiday_work_reason = data.get('holiday_work_reason', '')

    if not date_str:
        return jsonify({
            'success': False,
            'error': 'Date is required.'
        }), 400

    try:
        check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({
            'success': False,
            'error': 'Invalid date format. Use YYYY-MM-DD.'
        }), 400

    try:
        # Validate holiday work data
        validation_result = AttendanceService.validate_holiday_work_data(
            current_user.employee_detail.id,
            check_date,
            holiday_work_reason
        )

        return jsonify({
            'success': True,
            'validation': validation_result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to validate holiday work data: {str(e)}'
        }), 500
